{"name": "youtube-looper-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPatterns=integration", "build:dev": "NODE_ENV=development next build", "build:prod": "NODE_ENV=production next build", "export": "next build", "export:dev": "NODE_ENV=development next build", "export:prod": "NODE_ENV=production next build", "firebase:emulators": "cd firebase/config && firebase emulators:start", "firebase:deploy:firestore": "cd firebase/config && firebase deploy --only firestore", "firebase:deploy:firestore:dev": "cd firebase/config && firebase deploy --only firestore --project development", "firebase:deploy:firestore:prod": "cd firebase/config && firebase deploy --only firestore --project production", "firebase:deploy:rules": "cd firebase/config && firebase deploy --only firestore:rules", "firebase:deploy:rules:dev": "cd firebase/config && firebase deploy --only firestore:rules --project development", "firebase:deploy:rules:prod": "cd firebase/config && firebase deploy --only firestore:rules --project production", "firebase:deploy:indexes": "cd firebase/config && firebase deploy --only firestore:indexes", "firebase:deploy:indexes:dev": "cd firebase/config && firebase deploy --only firestore:indexes --project development", "firebase:deploy:indexes:prod": "cd firebase/config && firebase deploy --only firestore:indexes --project production", "env:check": "node scripts/check-env.js", "env:copy": "cp .env.example .env.local", "setup:password": "node firebase/scripts/setup-password.js", "deploy:firestore": "node firebase/scripts/deploy-firebase.js", "deploy:firestore:dev": "node firebase/scripts/deploy-firebase.js development firestore", "deploy:firestore:prod": "node firebase/scripts/deploy-firebase.js production firestore"}, "dependencies": {"@firebase/ai": "^2.0.0", "@hello-pangea/dnd": "^18.0.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "bcryptjs": "^3.0.2", "dotenv": "^17.2.1", "firebase": "^12.0.0", "next": "15.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.4.4", "firebase-tools": "^14.11.1", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8", "tailwindcss": "^3.3.0"}}
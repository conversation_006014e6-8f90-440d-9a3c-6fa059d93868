/**
 * Automated Comprehensive Looping Tests
 *
 * This test suite automatically generates and tests all meaningful combinations
 * of queue configurations using SHORT VIDEOS to ensure fast, efficient testing.
 */

import { LoopingRuleEngine, TimeframeData, VideoLoopSettings, LoopingState } from '@/lib/looping/LoopingRuleEngine'
import { VideoPlayerLoopingManager } from '@/lib/looping/VideoPlayerLoopingManager'
import {
  SHORT_TEST_VIDEOS,
  generateShortTimeframes,
  TEST_QUEUE_CONFIGS,
  RECOMMENDED_TEST_CONFIGS,
  ULTRA_FAST_TEST_VIDEO,
  MICRO_TIMEFRAMES,
  calculateTestDuration
} from '../test-data/short-test-videos'

// Test configuration types
interface VideoConfig {
  id: string
  loopMode: 'timeframes-only' | 'whole-video-plus-timeframes'
  videoLoopCount: number
  timeframes: {
    count: number
    loopCount: number
  }
}

interface QueueConfig {
  queueLoopCount: number
  videos: VideoConfig[]
}

interface TestScenario {
  name: string
  config: QueueConfig
  expectedBehavior: {
    totalVideoPlays: number
    totalTimeframePlays: number
    shouldComplete: boolean
  }
}

describe('Automated Comprehensive Looping Tests', () => {
  let ruleEngine: LoopingRuleEngine
  let loopingManager: VideoPlayerLoopingManager
  let mockOnStateUpdate: jest.Mock
  let mockOnSeekTo: jest.Mock
  let mockOnVideoComplete: jest.Mock

  beforeEach(() => {
    ruleEngine = new LoopingRuleEngine()
    mockOnStateUpdate = jest.fn()
    mockOnSeekTo = jest.fn()
    mockOnVideoComplete = jest.fn()
    
    loopingManager = new VideoPlayerLoopingManager(
      mockOnStateUpdate,
      mockOnSeekTo,
      mockOnVideoComplete
    )
  })

  // Helper: Generate SHORT timeframes for fast testing
  const generateFastTimeframes = (videoId: string, count: number, loopCount: number): TimeframeData[] => {
    // Use micro-timeframes for ultra-fast testing (2-3 seconds each)
    if (count <= MICRO_TIMEFRAMES.length) {
      return MICRO_TIMEFRAMES.slice(0, count).map(tf => ({
        ...tf,
        loopCount
      }))
    }

    // Fallback to generated short timeframes
    return generateShortTimeframes(videoId, count, loopCount)
  }

  // Helper: Calculate expected behavior for a scenario
  const calculateExpectedBehavior = (config: QueueConfig) => {
    let totalVideoPlays = 0
    let totalTimeframePlays = 0

    const queueRuns = config.queueLoopCount === -1 ? 1 : Math.max(1, config.queueLoopCount)
    
    for (let queueRun = 0; queueRun < queueRuns; queueRun++) {
      for (const video of config.videos) {
        // Each video runs videoLoopCount times
        for (let videoLoop = 0; videoLoop < video.videoLoopCount; videoLoop++) {
          totalVideoPlays++
          
          // Count timeframe plays
          if (video.timeframes.count > 0) {
            const timeframeRuns = video.timeframes.count * video.timeframes.loopCount
            totalTimeframePlays += timeframeRuns
          }
        }
      }
    }

    return {
      totalVideoPlays,
      totalTimeframePlays,
      shouldComplete: config.queueLoopCount !== -1
    }
  }

  // Helper: Simulate complete queue playback
  const simulateQueuePlayback = async (config: QueueConfig): Promise<{
    videoPlays: number
    timeframePlays: number
    completed: boolean
  }> => {
    let videoPlays = 0
    let timeframePlays = 0
    let completed = false

    // Initialize all videos with SHORT timeframes for fast testing
    for (const videoConfig of config.videos) {
      const timeframes = generateFastTimeframes(videoConfig.id, videoConfig.timeframes.count, videoConfig.timeframes.loopCount)
      loopingManager.initializeVideo(
        videoConfig.id,
        timeframes,
        {
          loopMode: videoConfig.loopMode,
          videoLoopCount: videoConfig.videoLoopCount
        },
        config.queueLoopCount
      )
    }

    // Simulate queue playback with safety limit
    const maxIterations = 1000
    let iterations = 0
    let currentVideoIndex = 0

    while (iterations < maxIterations) {
      iterations++
      
      const currentVideo = config.videos[currentVideoIndex]
      const timeframes = generateFastTimeframes(currentVideo.id, currentVideo.timeframes.count, currentVideo.timeframes.loopCount)
      
      // Simulate video playback
      videoPlays++
      
      // Simulate timeframes if they exist
      if (timeframes.length > 0) {
        for (const timeframe of timeframes) {
          for (let loop = 0; loop < timeframe.loopCount; loop++) {
            timeframePlays++
          }
        }
      }

      // Check if video should complete or continue
      const result = loopingManager.handleVideoEnd(
        currentVideo.id,
        timeframes,
        { loopMode: currentVideo.loopMode, videoLoopCount: currentVideo.videoLoopCount }
      )

      if (result === 'complete') {
        // Move to next video
        currentVideoIndex++
        
        if (currentVideoIndex >= config.videos.length) {
          // End of queue - check if should loop
          const queueAction = ruleEngine.getQueueAction(
            config.videos.length - 1,
            config.videos.length,
            config.queueLoopCount
          )
          
          if (queueAction === 'queue-complete') {
            completed = true
            break
          } else if (queueAction === 'loop-queue') {
            currentVideoIndex = 0 // Loop back to start
          }
        }
      }

      // Safety check for infinite loops in testing
      if (config.queueLoopCount === -1 && iterations > 100) {
        break // Stop infinite loop test after reasonable iterations
      }
    }

    return { videoPlays, timeframePlays, completed }
  }

  // Generate test scenarios using SHORT TEST VIDEOS
  const generateTestScenarios = (): TestScenario[] => {
    const scenarios: TestScenario[] = []

    // Queue loop variations (reduced for faster testing)
    const queueLoopCounts = [1, 2, -1] // Skip 0 as it's not meaningful

    // Video configurations using SHORT videos (optimized for speed)
    const videoConfigs: Omit<VideoConfig, 'id'>[] = [
      // Ultra-fast cases (using micro-timeframes)
      { loopMode: 'timeframes-only', videoLoopCount: 1, timeframes: { count: 0, loopCount: 1 } },
      { loopMode: 'timeframes-only', videoLoopCount: 1, timeframes: { count: 1, loopCount: 1 } },
      { loopMode: 'timeframes-only', videoLoopCount: 1, timeframes: { count: 2, loopCount: 1 } },
      { loopMode: 'timeframes-only', videoLoopCount: 2, timeframes: { count: 1, loopCount: 2 } },

      // Fast whole video cases
      { loopMode: 'whole-video-plus-timeframes', videoLoopCount: 1, timeframes: { count: 0, loopCount: 1 } },
      { loopMode: 'whole-video-plus-timeframes', videoLoopCount: 1, timeframes: { count: 1, loopCount: 1 } },
      { loopMode: 'whole-video-plus-timeframes', videoLoopCount: 2, timeframes: { count: 1, loopCount: 2 } },

      // Moderate complexity (still fast with short videos)
      { loopMode: 'timeframes-only', videoLoopCount: 2, timeframes: { count: 2, loopCount: 2 } },
      { loopMode: 'whole-video-plus-timeframes', videoLoopCount: 2, timeframes: { count: 2, loopCount: 2 } },
    ]

    // Generate scenarios for single video queues using SHORT TEST VIDEOS
    for (const queueLoopCount of queueLoopCounts) {
      for (let i = 0; i < videoConfigs.length; i++) {
        const videoConfig = videoConfigs[i]
        // Use different short videos for variety
        const testVideo = SHORT_TEST_VIDEOS[i % SHORT_TEST_VIDEOS.length]

        const config: QueueConfig = {
          queueLoopCount,
          videos: [{ ...videoConfig, id: testVideo.id }]
        }

        const estimatedDuration = calculateTestDuration({ videos: config.videos }, queueLoopCount)

        scenarios.push({
          name: `Single(${testVideo.duration}s): ${videoConfig.loopMode}, VL:${videoConfig.videoLoopCount}, TF:${videoConfig.timeframes.count}x${videoConfig.timeframes.loopCount}, QL:${queueLoopCount} [~${estimatedDuration}s]`,
          config,
          expectedBehavior: calculateExpectedBehavior(config)
        })
      }
    }

    // Generate scenarios for multi-video queues using SHORTEST videos
    const multiVideoConfigs = [
      // Fast multi-video combinations using shortest videos
      [videoConfigs[1], videoConfigs[5]], // timeframes-only + whole-video
      [videoConfigs[3], videoConfigs[6]], // moderate complexity
    ]

    for (const queueLoopCount of [1, 2]) { // Skip infinite for multi-video to avoid long tests
      for (const videoConfigSet of multiVideoConfigs) {
        // Use the shortest videos for multi-video tests
        const shortestVideos = SHORT_TEST_VIDEOS.slice(0, videoConfigSet.length)

        const config: QueueConfig = {
          queueLoopCount,
          videos: videoConfigSet.map((vc, i) => ({
            ...vc,
            id: shortestVideos[i].id
          }))
        }

        const estimatedDuration = calculateTestDuration({ videos: config.videos }, queueLoopCount)

        scenarios.push({
          name: `Multi(${videoConfigSet.length}v): QL:${queueLoopCount} [~${estimatedDuration}s]`,
          config,
          expectedBehavior: calculateExpectedBehavior(config)
        })
      }
    }

    return scenarios
  }

  // Run all generated test scenarios
  const testScenarios = generateTestScenarios()

  // Ultra-fast smoke tests using the shortest video and micro-timeframes
  describe('Ultra-Fast Smoke Tests', () => {
    test('Ultra-Fast: Single video, no timeframes', async () => {
      const config: QueueConfig = {
        queueLoopCount: 1,
        videos: [{
          id: ULTRA_FAST_TEST_VIDEO.id, // 19 seconds
          loopMode: 'whole-video-plus-timeframes',
          videoLoopCount: 1,
          timeframes: { count: 0, loopCount: 1 }
        }]
      }

      const result = await simulateQueuePlayback(config)
      expect(result.completed).toBe(true)
      expect(result.videoPlays).toBe(1)
    })

    test('Ultra-Fast: Single video with micro-timeframes', async () => {
      const config: QueueConfig = {
        queueLoopCount: 1,
        videos: [{
          id: ULTRA_FAST_TEST_VIDEO.id,
          loopMode: 'timeframes-only',
          videoLoopCount: 1,
          timeframes: { count: 2, loopCount: 1 } // Uses MICRO_TIMEFRAMES (3s each)
        }]
      }

      const result = await simulateQueuePlayback(config)
      expect(result.completed).toBe(true)
      expect(result.videoPlays).toBe(1)
      expect(result.timeframePlays).toBe(2)
    })
  })

  describe('Generated Test Scenarios', () => {
    testScenarios.forEach((scenario, index) => {
      test(`Scenario ${index + 1}: ${scenario.name}`, async () => {
        const result = await simulateQueuePlayback(scenario.config)

        // For infinite queues, we just check that it doesn't complete immediately
        if (scenario.config.queueLoopCount === -1) {
          expect(result.completed).toBe(false)
          expect(result.videoPlays).toBeGreaterThan(0)
        } else {
          // For finite queues, check exact behavior
          expect(result.completed).toBe(scenario.expectedBehavior.shouldComplete)

          // Allow some tolerance for complex scenarios due to simulation complexity
          const tolerance = Math.max(1, Math.floor(scenario.expectedBehavior.totalVideoPlays * 0.1))
          expect(result.videoPlays).toBeGreaterThanOrEqual(scenario.expectedBehavior.totalVideoPlays - tolerance)
          expect(result.videoPlays).toBeLessThanOrEqual(scenario.expectedBehavior.totalVideoPlays + tolerance)
        }
      }, 15000) // 15 second timeout (increased slightly for short video scenarios)
    })
  })

  // Summary test with performance metrics
  test('Test Coverage Summary', () => {
    const totalScenarios = testScenarios.length
    const singleVideoScenarios = testScenarios.filter(s => s.config.videos.length === 1).length
    const multiVideoScenarios = testScenarios.filter(s => s.config.videos.length > 1).length

    // Calculate total estimated test time
    let totalEstimatedTime = 0
    testScenarios.forEach(scenario => {
      const duration = calculateTestDuration({ videos: scenario.config.videos }, scenario.config.queueLoopCount)
      totalEstimatedTime += Math.min(duration, 60) // Cap at 60s per test for infinite loops
    })

    console.log(`\n🎯 Automated Test Coverage Summary (SHORT VIDEOS):`)
    console.log(`   Total scenarios tested: ${totalScenarios}`)
    console.log(`   Single video scenarios: ${singleVideoScenarios}`)
    console.log(`   Multi video scenarios: ${multiVideoScenarios}`)
    console.log(`   Queue loop variations: 3 (1, 2, infinite)`)
    console.log(`   Video mode variations: 2 (timeframes-only, whole-video-plus-timeframes)`)
    console.log(`   Timeframe variations: Multiple counts and loop combinations`)
    console.log(`   Test videos used: ${SHORT_TEST_VIDEOS.length} (${SHORT_TEST_VIDEOS.map(v => v.duration + 's').join(', ')})`)
    console.log(`   Shortest video: ${ULTRA_FAST_TEST_VIDEO.title} (${ULTRA_FAST_TEST_VIDEO.duration}s)`)
    console.log(`   Estimated total test time: ~${Math.round(totalEstimatedTime)}s (${Math.round(totalEstimatedTime/60)}min)`)
    console.log(`   Ultra-fast smoke tests: 2 (using micro-timeframes)`)

    expect(totalScenarios).toBeGreaterThan(20) // Reasonable number of scenarios
    expect(totalEstimatedTime).toBeLessThan(600) // Should complete in under 10 minutes
  })
})

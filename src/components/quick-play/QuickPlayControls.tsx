'use client'

import { useQuickPlay } from '@/hooks/useQuickPlay'
import { useI18n } from '@/hooks/useI18n'

export function QuickPlayControls() {
  const {
    currentVideo,
    isPlaying,
    isLooping,
    volume,
    play,
    pause,
    setVolume,
    toggleLoop,
    clearVideo
  } = useQuickPlay()
  
  const { t } = useI18n()

  const hasVideo = !!currentVideo

  return (
    <div className="glassmorphism rounded-2xl p-4">
      <div className="flex items-center justify-between">
        {/* Left side - Video info */}
        <div className="flex-1 min-w-0">
          {currentVideo ? (
            <div className="flex items-center space-x-3">
              <img
                src={currentVideo.thumbnail}
                alt={currentVideo.title}
                className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
              />
              <div className="min-w-0 flex-1">
                <h3 className="text-sm font-medium text-white truncate">
                  {currentVideo.title}
                </h3>
                <p className="text-xs text-dark-300 truncate">
                  {currentVideo.channel}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-dark-400 text-sm">
              {t('quickPlay.noVideoLoaded')}
            </div>
          )}
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center space-x-2 ml-4">
          {/* Play/Pause */}
          <button
            onClick={isPlaying ? pause : play}
            disabled={!hasVideo}
            className="p-2 rounded-lg bg-primary-600 text-white hover:bg-primary-700 disabled:opacity-30 disabled:cursor-not-allowed transition-colors duration-200"
            title={isPlaying ? t('controls.pause') : t('controls.play')}
          >
            {isPlaying ? (
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
              </svg>
            ) : (
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
            )}
          </button>

          {/* Loop Toggle */}
          <button
            onClick={toggleLoop}
            disabled={!hasVideo}
            className={`p-2 rounded-lg transition-colors duration-200 disabled:opacity-30 disabled:cursor-not-allowed ${
              isLooping
                ? 'bg-primary-600 text-white shadow-lg'
                : 'text-dark-400 hover:text-white hover:bg-white/10'
            }`}
            title={isLooping ? t('quickPlay.disableLoop') : t('quickPlay.enableLoop')}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
            </svg>
          </button>

          {/* Volume Control */}
          <div className="flex items-center space-x-2">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
              <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
            </svg>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              disabled={!hasVideo}
              className="w-20 h-1 bg-dark-600 rounded-lg appearance-none cursor-pointer disabled:opacity-30 disabled:cursor-not-allowed"
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${volume * 100}%, #374151 ${volume * 100}%, #374151 100%)`
              }}
            />
          </div>

          {/* Clear Video */}
          {hasVideo && (
            <button
              onClick={clearVideo}
              className="p-2 rounded-lg text-dark-400 hover:text-white hover:bg-white/10 transition-colors duration-200"
              title={t('quickPlay.clearVideo')}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

import {
  doc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  writeBatch,
  deleteDoc
} from 'firebase/firestore'
import { deleteUser, User as FirebaseUser } from 'firebase/auth'
import { FirebaseService } from './firebase'

/**
 * Firebase Account Service
 * Handles user account management operations including account deletion
 */
export class FirebaseAccountService {
  private firebaseService: FirebaseService

  constructor() {
    this.firebaseService = new FirebaseService()
  }

  /**
   * Get the Firestore database instance
   */
  private getDb() {
    return this.firebaseService.getDb()
  }

  /**
   * Get user profile data
   */
  async getUserProfile(userId: string) {
    const db = this.getDb()
    if (!db) {
      throw new Error('Database not available')
    }

    try {
      const userRef = doc(db, 'users', userId)
      const userDoc = await getDoc(userRef)
      
      if (userDoc.exists()) {
        return { id: userDoc.id, ...userDoc.data() }
      }
      
      return null
    } catch (error) {
      console.error('Error fetching user profile:', error)
      throw new Error('Failed to fetch user profile')
    }
  }

  /**
   * Get user statistics (queue counts, etc.)
   */
  async getUserStatistics(userId: string) {
    const db = this.getDb()
    if (!db) {
      throw new Error('Database not available')
    }

    try {
      // Get user queues
      const userQueuesQuery = query(
        collection(db, 'queues'),
        where('userId', '==', userId)
      )
      
      const userQueuesSnapshot = await getDocs(userQueuesQuery)
      const queues = userQueuesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))

      // Calculate statistics
      const stats = {
        totalQueues: queues.length,
        publicQueues: queues.filter((q: any) => q.isPublic).length,
        privateQueues: queues.filter((q: any) => !q.isPublic).length,
        totalVideos: queues.reduce((total: number, queue: any) => 
          total + (queue.metadata?.videoCount || queue.queueData?.items?.length || 0), 0
        ),
        queues: queues
      }

      return stats
    } catch (error) {
      console.error('Error fetching user statistics:', error)
      throw new Error('Failed to fetch user statistics')
    }
  }

  /**
   * Delete all user data from Firestore
   * This includes user profile, queues, and any other user-related data
   */
  async deleteUserData(userId: string): Promise<void> {
    const db = this.getDb()
    if (!db) {
      throw new Error('Database not available')
    }

    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid user ID provided')
    }

    try {
      console.log('🗑️ Starting user data deletion for user:', userId)

      // Use a batch to ensure all operations succeed or fail together
      const batch = writeBatch(db)
      let totalOperations = 0

      // 1. Delete user profile
      const userRef = doc(db, 'users', userId)
      const userDoc = await getDoc(userRef)
      if (userDoc.exists()) {
        batch.delete(userRef)
        totalOperations++
        console.log('📝 Queued user profile deletion')
      }

      // 2. Get all user queues for deletion
      const userQueuesQuery = query(
        collection(db, 'queues'),
        where('userId', '==', userId)
      )
      
      const userQueuesSnapshot = await getDocs(userQueuesQuery)
      console.log(`📝 Found ${userQueuesSnapshot.size} queues to delete`)

      // Add queue deletions to batch
      userQueuesSnapshot.forEach((queueDoc) => {
        batch.delete(queueDoc.ref)
        totalOperations++
      })

      // 3. Check for any data in personal_queues collection (legacy)
      const personalQueuesQuery = query(
        collection(db, 'personal_queues'),
        where('userId', '==', userId)
      )
      
      const personalQueuesSnapshot = await getDocs(personalQueuesQuery)
      console.log(`📝 Found ${personalQueuesSnapshot.size} personal queues to delete`)

      // Add personal queue deletions to batch
      personalQueuesSnapshot.forEach((queueDoc) => {
        batch.delete(queueDoc.ref)
        totalOperations++
      })

      // 4. Check for any user preferences or settings
      const userPreferencesQuery = query(
        collection(db, 'user_preferences'),
        where('userId', '==', userId)
      )
      
      const userPreferencesSnapshot = await getDocs(userPreferencesQuery)
      console.log(`📝 Found ${userPreferencesSnapshot.size} user preferences to delete`)

      // Add user preferences deletions to batch
      userPreferencesSnapshot.forEach((prefDoc) => {
        batch.delete(prefDoc.ref)
        totalOperations++
      })

      // Execute all deletions in a single batch
      if (totalOperations > 0) {
        await batch.commit()
        console.log(`✅ ${totalOperations} user data records deleted from Firestore`)
      } else {
        console.log('ℹ️ No user data found to delete')
      }

    } catch (error: any) {
      console.error('❌ Error deleting user data from Firestore:', error)
      
      // Provide more specific error information
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied: Unable to delete user data')
      } else if (error.code === 'unavailable') {
        throw new Error('Database temporarily unavailable. Please try again.')
      } else {
        throw new Error('Failed to delete user data from database')
      }
    }
  }

  /**
   * Delete Firebase Authentication user
   */
  async deleteAuthUser(firebaseUser: FirebaseUser): Promise<void> {
    try {
      console.log('🗑️ Deleting Firebase Auth user:', firebaseUser.uid)
      await deleteUser(firebaseUser)
      console.log('✅ Firebase Auth user deleted successfully')
    } catch (error: any) {
      console.error('❌ Error deleting Firebase Auth user:', error)
      
      if (error.code === 'auth/requires-recent-login') {
        throw new Error('For security, please sign out and sign back in before deleting your account.')
      } else if (error.code === 'auth/user-not-found') {
        throw new Error('User account not found.')
      } else {
        throw new Error('Failed to delete user authentication')
      }
    }
  }

  /**
   * Complete account deletion process
   * Deletes both Firestore data and Firebase Auth user
   */
  async deleteUserAccount(userId: string, firebaseUser: FirebaseUser): Promise<void> {
    try {
      console.log('🗑️ Starting complete account deletion process for user:', userId)

      // Verify user is still authenticated and owns the account
      if (firebaseUser.uid !== userId) {
        throw new Error('User authentication mismatch')
      }

      // First delete all user data from Firestore
      await this.deleteUserData(userId)

      // Then delete the Firebase Auth user
      await this.deleteAuthUser(firebaseUser)
      
      console.log('✅ Complete account deletion process completed successfully')

    } catch (error: any) {
      console.error('❌ Complete account deletion error:', error)
      
      // Re-throw with context
      if (error.message?.includes('authentication mismatch')) {
        throw error
      } else if (error.message?.includes('recent login')) {
        throw error
      } else if (error.message?.includes('permission')) {
        throw error
      } else if (error.message?.includes('database') || error.message?.includes('Firestore')) {
        throw new Error('Failed to delete your data. Please try again or contact support.')
      } else if (error.message?.includes('network') || error.message?.includes('offline')) {
        throw new Error('Network error. Please check your connection and try again.')
      } else {
        throw new Error('Failed to delete account. Please try again.')
      }
    }
  }

  /**
   * Validate if user can delete their account
   * Checks for recent login and other security requirements
   */
  validateAccountDeletion(firebaseUser: FirebaseUser): { canDelete: boolean; reason?: string } {
    // Check if user is anonymous
    if (firebaseUser.isAnonymous) {
      return {
        canDelete: false,
        reason: 'Cannot delete anonymous accounts'
      }
    }

    // Check for recent login (within last 5 minutes for security)
    const lastSignInTime = firebaseUser.metadata.lastSignInTime
    if (lastSignInTime) {
      const lastSignIn = new Date(lastSignInTime).getTime()
      const fiveMinutesAgo = Date.now() - (5 * 60 * 1000)
      
      if (lastSignIn < fiveMinutesAgo) {
        return {
          canDelete: true,
          reason: 'For security, you may need to sign out and sign back in if deletion fails.'
        }
      }
    }

    return { canDelete: true }
  }
}

// Export a singleton instance
export const firebaseAccountService = new FirebaseAccountService()

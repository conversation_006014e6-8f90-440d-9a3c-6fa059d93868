# Test Documentation

This document provides comprehensive instructions for running and understanding the test suite in this Next.js project.

## Quick Start

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Test Structure

The project uses **Jest** as the testing framework with **React Testing Library** for component testing. Tests are organized in the following structure:

```
src/__tests__/
├── auth/                    # Authentication-related tests
├── components/              # Component unit tests  
├── i18n/                   # Internationalization tests
├── integration/            # Integration tests
└── settings/               # Settings component tests
```

## Running Tests

### Run All Tests
```bash
npm test
```

### Run Specific Test Files
```bash
# Run a specific test file
npm test -- src/__tests__/auth/credential-already-in-use.test.ts

# Run tests matching a pattern
npm test -- --testPathPattern=i18n

# Run tests in a specific directory
npm test -- src/__tests__/components/
```

### Run Tests with Different Options
```bash
# Run tests in watch mode (re-runs on file changes)
npm test -- --watch

# Run tests with verbose output (shows individual test names)
npm test -- --verbose

# Run tests with coverage report
npm test -- --coverage

# Run tests without cache (useful for troubleshooting)
npm test -- --no-cache

# Run only changed tests (in git repositories)
npm test -- --onlyChanged
```

## Test Categories

### 1. Unit Tests

**Component Tests** (`src/__tests__/components/`)
- Test individual React components in isolation
- Use React Testing Library for rendering and interaction
- Require I18nProvider wrapper for components using translations

**Utility Tests** (`src/__tests__/auth/`)
- Test individual functions and utilities
- Focus on specific functionality like authentication flows

### 2. Integration Tests (`src/__tests__/integration/`)
- Test complex interactions between multiple components/systems
- Include looping logic, video playback scenarios
- May require more setup and longer execution times

### 3. Internationalization Tests (`src/__tests__/i18n/`)
- Test translation loading and rendering
- Test language switching functionality
- Test fallback behavior for missing translations

## Environment Setup

### Required Environment Variables
The following environment variables are needed for tests (copy from `.env.example`):

```bash
NEXT_PUBLIC_YOUTUBE_API_KEY=your_youtube_api_key
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
```

**Note**: Tests will show warnings about missing environment variables but will still run. The warnings can be ignored for most test scenarios.

### Test Dependencies
Tests automatically install and use these key dependencies:
- **Jest**: Test runner and assertion library
- **React Testing Library**: Component testing utilities
- **@testing-library/jest-dom**: Additional Jest matchers
- **jsdom**: DOM environment for Node.js testing

## Expected Test Output

### When Tests Pass ✅
```
PASS src/__tests__/auth/credential-already-in-use.test.ts
PASS src/__tests__/i18n/i18n.test.tsx
PASS src/__tests__/settings/AccountDeletion.test.tsx

Test Suites: 3 passed, 3 total
Tests:       22 passed, 22 total
Snapshots:   0 total
Time:        2.5s
```

### When Tests Fail ❌
```
FAIL src/__tests__/components/personal-queue-privacy-toggle.test.tsx
  ● Personal Queue Privacy Toggle › should sync local isPublic state

    TestingLibraryElementError: Unable to find element with text: /make public/i
    
    This could be because the text is broken up by multiple elements.
```

## Current Test Status

### ✅ Passing Tests
- `auth/credential-already-in-use.test.ts` - All tests pass
- `i18n/i18n.test.tsx` - All 14 tests pass  
- `settings/AccountDeletion.test.tsx` - All 5 tests pass

### ⚠️ Partially Working Tests
- `components/personal-queue-privacy-toggle.test.tsx` - 2/3 tests pass
  - Issue: One test expects English text but gets translation keys
  - Fix needed: Update test assertions to match actual rendered content

### ❌ Tests Requiring Investigation
- `integration/looping.test.ts` - 7/11 tests failing
  - Issue: Logic mismatches between test expectations and implementation
  - Action needed: Requires domain expert review

## Troubleshooting Common Issues

### Issue: "useI18n must be used within an I18nProvider"
**Solution**: Wrap your component with I18nProvider in tests:
```javascript
import { I18nProvider } from '@/components/providers/I18nProvider'

const renderWithProviders = (component) => {
  return render(
    <I18nProvider>
      {component}
    </I18nProvider>
  )
}
```

### Issue: Translation warnings in test output
**Expected Behavior**: Tests show warnings like "Translation missing for key: labels.videos"
**Solution**: This is normal - tests run without full translation files loaded. Tests should still pass.

### Issue: Tests timeout or hang
**Possible Causes**:
- Integration tests with complex logic
- Infinite loops in test code
- Missing mocks for external dependencies

**Solutions**:
- Run individual test files instead of entire suite
- Increase timeout: `npm test -- --testTimeout=10000`
- Check for missing mocks

### Issue: Environment variable warnings
**Expected Behavior**: Tests show warnings about missing Firebase/YouTube API keys
**Solution**: Copy `.env.example` to `.env.local` and fill in values, or ignore warnings for most tests

## Writing New Tests

### Component Test Template
```javascript
import React from 'react'
import { render, screen } from '@testing-library/react'
import { I18nProvider } from '@/components/providers/I18nProvider'
import { YourComponent } from '@/components/YourComponent'

const renderWithProviders = (component) => {
  return render(
    <I18nProvider>
      {component}
    </I18nProvider>
  )
}

describe('YourComponent', () => {
  it('should render correctly', () => {
    renderWithProviders(<YourComponent />)
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })
})
```

### Best Practices
1. **Always use providers**: Wrap components with required providers (I18nProvider, etc.)
2. **Test behavior, not implementation**: Focus on what users see and do
3. **Use descriptive test names**: Clearly describe what the test verifies
4. **Mock external dependencies**: Mock Firebase, API calls, etc.
5. **Keep tests focused**: One test should verify one specific behavior

## Continuous Integration

Tests are designed to run in CI/CD environments. Key considerations:
- Tests run in Node.js environment with jsdom
- No real browser required
- Environment variables should be set in CI configuration
- Tests should complete within reasonable time limits

## Getting Help

If you encounter test failures:
1. **Check this documentation** for common solutions
2. **Run individual test files** to isolate issues
3. **Check test output carefully** for specific error messages
4. **For integration test failures**: These may require domain expertise to resolve
5. **For new test failures after code changes**: Verify your changes don't break existing functionality

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { I18nProvider } from '@/components/providers/I18nProvider'
import { useI18n } from '@/hooks/useI18n'
import { loadTranslations } from '@/lib/i18n/utils'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Test component for Spanish translations
function SpanishTestComponent() {
  const { t, language, setLanguage, availableLanguages, isLoading } = useI18n()

  if (isLoading) {
    return <div data-testid="loading">Loading...</div>
  }

  return (
    <div>
      <div data-testid="current-language">{language}</div>
      <div data-testid="available-languages-count">{availableLanguages.length}</div>
      
      {/* Navigation translations */}
      <div data-testid="nav-create-queue">{t('navigation.createQueue')}</div>
      <div data-testid="nav-magic-queue">{t('navigation.magicQueue')}</div>
      <div data-testid="nav-my-queues">{t('navigation.myQueues')}</div>
      <div data-testid="nav-settings">{t('common.settings')}</div>
      
      {/* Common translations */}
      <div data-testid="common-loading">{t('common.loading')}</div>
      <div data-testid="common-save">{t('common.save')}</div>
      <div data-testid="common-cancel">{t('common.cancel')}</div>
      
      {/* Settings translations */}
      <div data-testid="settings-title">{t('settings.title')}</div>
      <div data-testid="settings-language-settings">{t('settings.languageSettings')}</div>
      
      {/* Time translations with interpolation */}
      <div data-testid="time-minutes-ago">{t('time.minutesAgo', { count: 5 })}</div>
      <div data-testid="time-hours-ago">{t('time.hoursAgo', { count: 2 })}</div>
      
      {/* Language switching buttons */}
      <button 
        data-testid="switch-to-english" 
        onClick={() => setLanguage('en')}
      >
        Switch to English
      </button>
      <button 
        data-testid="switch-to-spanish" 
        onClick={() => setLanguage('es')}
      >
        Switch to Spanish
      </button>
      
      {/* Available languages */}
      <div data-testid="available-languages">
        {availableLanguages.map(lang => (
          <div key={lang.code} data-testid={`lang-${lang.code}`}>
            {lang.flag} {lang.name} ({lang.nativeName})
          </div>
        ))}
      </div>
    </div>
  )
}

describe('Spanish Language Support', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  describe('Spanish Translation Loading', () => {
    it('should load Spanish translations correctly', async () => {
      const spanishTranslations = await loadTranslations('es')
      
      expect(spanishTranslations).toBeDefined()
      expect(spanishTranslations.common).toBeDefined()
      expect(spanishTranslations.common.loading).toBe('Cargando...')
      expect(spanishTranslations.common.save).toBe('Guardar')
      expect(spanishTranslations.navigation.createQueue).toBe('Crear Cola')
      expect(spanishTranslations.settings.title).toBe('Configuración de Cuenta')
    })

    it('should have all required translation keys', async () => {
      const englishTranslations = await loadTranslations('en')
      const spanishTranslations = await loadTranslations('es')
      
      // Check that Spanish has the same top-level keys as English
      const englishKeys = Object.keys(englishTranslations)
      const spanishKeys = Object.keys(spanishTranslations)
      
      expect(spanishKeys).toEqual(expect.arrayContaining(englishKeys))
      
      // Check some nested keys
      expect(spanishTranslations.common.loading).toBeDefined()
      expect(spanishTranslations.navigation.createQueue).toBeDefined()
      expect(spanishTranslations.settings.title).toBeDefined()
      expect(spanishTranslations.time.minutesAgo).toBeDefined()
    })
  })

  describe('Language Switching', () => {
    it('should show both English and Spanish as available languages', async () => {
      render(
        <I18nProvider>
          <SpanishTestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      expect(screen.getByTestId('available-languages-count')).toHaveTextContent('3')
      expect(screen.getByTestId('lang-en')).toHaveTextContent('🇺🇸 English (English)')
      expect(screen.getByTestId('lang-es')).toHaveTextContent('🇪🇸 Spanish (Español)')
    })

    it('should switch from English to Spanish and update translations', async () => {
      render(
        <I18nProvider>
          <SpanishTestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Initially should be English
      expect(screen.getByTestId('current-language')).toHaveTextContent('en')
      expect(screen.getByTestId('common-loading')).toHaveTextContent('Loading...')
      expect(screen.getByTestId('nav-create-queue')).toHaveTextContent('Create Queue')

      // Switch to Spanish
      fireEvent.click(screen.getByTestId('switch-to-spanish'))

      await waitFor(() => {
        expect(screen.getByTestId('current-language')).toHaveTextContent('es')
      })

      // Verify Spanish translations
      expect(screen.getByTestId('common-loading')).toHaveTextContent('Cargando...')
      expect(screen.getByTestId('common-save')).toHaveTextContent('Guardar')
      expect(screen.getByTestId('nav-create-queue')).toHaveTextContent('Crear Cola')
      expect(screen.getByTestId('nav-magic-queue')).toHaveTextContent('Cola Mágica')
      expect(screen.getByTestId('settings-title')).toHaveTextContent('Configuración de Cuenta')
    })

    it('should handle interpolation in Spanish', async () => {
      render(
        <I18nProvider>
          <SpanishTestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Switch to Spanish
      fireEvent.click(screen.getByTestId('switch-to-spanish'))

      await waitFor(() => {
        expect(screen.getByTestId('current-language')).toHaveTextContent('es')
      })

      // Check interpolated time strings
      expect(screen.getByTestId('time-minutes-ago')).toHaveTextContent('Hace 5 minutos')
      expect(screen.getByTestId('time-hours-ago')).toHaveTextContent('Hace 2 horas')
    })

    it('should switch back from Spanish to English', async () => {
      mockLocalStorage.getItem.mockReturnValue('es') // Start with Spanish

      render(
        <I18nProvider>
          <SpanishTestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Should start with Spanish
      expect(screen.getByTestId('current-language')).toHaveTextContent('es')
      expect(screen.getByTestId('common-loading')).toHaveTextContent('Cargando...')

      // Switch back to English
      fireEvent.click(screen.getByTestId('switch-to-english'))

      await waitFor(() => {
        expect(screen.getByTestId('current-language')).toHaveTextContent('en')
      })

      // Verify English translations are back
      expect(screen.getByTestId('common-loading')).toHaveTextContent('Loading...')
      expect(screen.getByTestId('nav-create-queue')).toHaveTextContent('Create Queue')
      expect(screen.getByTestId('settings-title')).toHaveTextContent('Account Settings')
    })
  })

  describe('Translation Comparison', () => {
    it('should have different translations for the same keys in English vs Spanish', async () => {
      const englishTranslations = await loadTranslations('en')
      const spanishTranslations = await loadTranslations('es')
      
      // Verify they are actually different
      expect(englishTranslations.common.loading).not.toBe(spanishTranslations.common.loading)
      expect(englishTranslations.navigation.createQueue).not.toBe(spanishTranslations.navigation.createQueue)
      expect(englishTranslations.settings.title).not.toBe(spanishTranslations.settings.title)
      
      // Verify Spanish translations are correct
      expect(spanishTranslations.common.loading).toBe('Cargando...')
      expect(spanishTranslations.navigation.createQueue).toBe('Crear Cola')
      expect(spanishTranslations.settings.title).toBe('Configuración de Cuenta')
    })
  })
})

'use client'

import { useState } from 'react'
import { useDebouncedTimeframe } from '@/hooks/useDebouncedTimeframe'
import { RangeSlider } from '@/components/ui/RangeSlider'

interface NewTimeframeFormProps {
  maxDuration: number
  onAdd: (startTime: number, endTime: number, loopCount: number) => void
  onCancel: () => void
}

export function NewTimeframeForm({ maxDuration, onAdd, onCancel }: NewTimeframeFormProps) {
  const [loopCount, setLoopCount] = useState(1)
  const [localStartTime, setLocalStartTime] = useState(0)
  const [localEndTime, setLocalEndTime] = useState(Math.min(30, maxDuration))

  const isValidTimeframe = localEndTime > localStartTime && localStartTime >= 0 && localEndTime <= maxDuration

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isValidTimeframe) {
      handleAddTimeframe()
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  const handleAddTimeframe = () => {
    if (isValidTimeframe) {
      onAdd(localStartTime, localEndTime, loopCount)
    }
  }

  const handleTimeframeChange = (start: number, end: number) => {
    setLocalStartTime(start)
    setLocalEndTime(end)
  }

  return (
    <div className="bg-dark-600/60 rounded-xl p-5 space-y-5 border border-dark-500/50 shadow-lg">
      <div>
        <label className="text-base text-dark-100 font-semibold block mb-4">Select Time Range</label>
        <RangeSlider
          min={0}
          max={maxDuration}
          startValue={localStartTime}
          endValue={localEndTime}
          onChange={handleTimeframeChange}
          step={1}
          className="mb-4"
        />
      </div>

      <div>
        <label className="text-sm text-dark-200 font-medium block mb-3">Loop Count</label>
        <input
          type="number"
          min="1"
          max="99"
          value={loopCount}
          onChange={(e) => setLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
          onKeyDown={handleKeyDown}
          className="input-field text-sm py-2 px-3 w-24"
        />
      </div>

      <div className="flex gap-3">
        <button
          onClick={handleAddTimeframe}
          disabled={!isValidTimeframe}
          className={`text-sm px-6 py-3 flex-1 rounded-lg font-medium transition-all shadow-lg ${
            isValidTimeframe
              ? 'btn-primary hover:scale-105 active:scale-95'
              : 'bg-dark-600 text-dark-400 cursor-not-allowed'
          }`}
        >
          Add Timeframe
        </button>
        <button
          onClick={onCancel}
          className="btn-secondary text-sm px-6 py-3 flex-1 rounded-lg font-medium transition-all hover:scale-105 active:scale-95 shadow-lg"
        >
          Cancel
        </button>
      </div>

      {!isValidTimeframe && (
        <div className="text-sm text-red-400 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
          End time must be greater than start time and within video duration
        </div>
      )}

      <div className="text-xs text-dark-400 text-center p-2 bg-dark-700/30 rounded-lg">
        Press <kbd className="bg-dark-600 px-1 rounded">Enter</kbd> to add, <kbd className="bg-dark-600 px-1 rounded">Escape</kbd> to cancel
      </div>
    </div>
  )
}

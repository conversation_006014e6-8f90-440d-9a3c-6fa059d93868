'use client'

import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/MainLayout'

export default function DocsPage() {
  const router = useRouter()

  const handleBackClick = () => {
    router.push('/')
  }

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto">
        {/* Close Button */}
        <div className="mb-6">
          <button
            onClick={handleBackClick}
            className="p-2 rounded-lg bg-white/5 hover:bg-white/10 text-dark-300 hover:text-white transition-all duration-200 group"
            title="Close Documentation"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="group-hover:rotate-90 transition-transform duration-200"
            >
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-primary-200 bg-clip-text text-transparent mb-4">
            Tubli Documentation
          </h1>
          <p className="text-dark-300 text-lg">
            Complete guide to using YouTube Looper features and loop modes
          </p>
        </div>

        {/* Table of Contents */}
        <div className="glassmorphism rounded-2xl p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-primary-400">Table of Contents</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <a href="#overview" className="text-dark-300 hover:text-primary-400 transition-colors">1. Overview</a>
            <a href="#getting-started" className="text-dark-300 hover:text-primary-400 transition-colors">2. Getting Started</a>
            <a href="#authentication" className="text-dark-300 hover:text-primary-400 transition-colors">3. Authentication</a>
            <a href="#search-videos" className="text-dark-300 hover:text-primary-400 transition-colors">4. Searching Videos</a>
            <a href="#loop-modes" className="text-dark-300 hover:text-primary-400 transition-colors">5. Loop Modes</a>
            <a href="#timeframes" className="text-dark-300 hover:text-primary-400 transition-colors">6. Timeframes</a>
            <a href="#queue-management" className="text-dark-300 hover:text-primary-400 transition-colors">7. Queue Management</a>
            <a href="#player-controls" className="text-dark-300 hover:text-primary-400 transition-colors">8. Player Controls</a>
            <a href="#sharing" className="text-dark-300 hover:text-primary-400 transition-colors">9. Sharing Queues</a>
            <a href="#tips" className="text-dark-300 hover:text-primary-400 transition-colors">10. Tips & Best Practices</a>
          </div>
        </div>

        {/* Content Sections */}
        <div className="space-y-8">
          {/* Overview */}
          <section id="overview" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">1. Overview</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                Tubli is a powerful YouTube video queue and looper application that allows you to create custom playlists 
                with advanced looping capabilities. Unlike regular playlists, Tubli gives you precise control over how 
                videos and specific sections (timeframes) within videos are looped.
              </p>
              <div className="bg-dark-800/50 rounded-lg p-4">
                <h3 className="font-semibold text-white mb-2">Key Features:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Real-time YouTube video search</li>
                  <li>Custom timeframe creation within videos</li>
                  <li>Two distinct loop modes for different use cases</li>
                  <li>Cloud-based queue storage and sharing</li>
                  <li>Anonymous and permanent account options</li>
                  <li>Drag-and-drop queue reordering</li>
                  <li>Public and private queue sharing</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Getting Started */}
          <section id="getting-started" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">2. Getting Started</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                When you first visit Tubli, you'll automatically be signed in with an anonymous account. 
                This allows you to start using all features immediately without any setup.
              </p>
              <div className="bg-primary-900/20 border border-primary-500/30 rounded-lg p-4">
                <h3 className="font-semibold text-primary-300 mb-2">Quick Start:</h3>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Use the search bar to find YouTube videos</li>
                  <li>Click the "+" button to add videos to your queue</li>
                  <li>Configure loop settings and timeframes as needed</li>
                  <li>Click "Create Queue" to save your playlist</li>
                  <li>Use the player controls to start playback</li>
                </ol>
              </div>
            </div>
          </section>

          {/* Authentication */}
          <section id="authentication" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">3. Authentication</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                Tubli supports both anonymous and permanent accounts with seamless data migration.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-yellow-300 mb-2">Anonymous Account</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Automatic sign-in on first visit</li>
                    <li>Full access to all features</li>
                    <li>Data stored temporarily</li>
                    <li>Yellow status indicator</li>
                  </ul>
                </div>
                
                <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-green-300 mb-2">Permanent Account</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Google or email sign-in</li>
                    <li>Permanent data storage</li>
                    <li>Cross-device synchronization</li>
                    <li>Green status indicator</li>
                  </ul>
                </div>
              </div>

              <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                <h3 className="font-semibold text-blue-300 mb-2">Data Migration</h3>
                <p className="text-sm">
                  When you upgrade from anonymous to permanent account, all your queues and data are 
                  automatically transferred. You won't lose any of your work!
                </p>
              </div>
            </div>
          </section>

          {/* Search Videos */}
          <section id="search-videos" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">4. Searching Videos</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                The search functionality uses YouTube's Data API to provide real-time video search results.
              </p>
              
              <div className="space-y-3">
                <div className="bg-dark-800/50 rounded-lg p-4">
                  <h3 className="font-semibold text-white mb-2">Search Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Real-time search as you type</li>
                    <li>Pagination for browsing more results</li>
                    <li>Video thumbnails, titles, and metadata</li>
                    <li>Duration and channel information</li>
                    <li>Direct add-to-queue functionality</li>
                  </ul>
                </div>
                
                <div className="bg-primary-900/20 border border-primary-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-primary-300 mb-2">Search Tips:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Use specific keywords for better results</li>
                    <li>Include artist names for music videos</li>
                    <li>Try different search terms if you don't find what you're looking for</li>
                    <li>Use the "Load More" button to see additional results</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* Loop Modes */}
          <section id="loop-modes" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">5. Loop Modes</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                Tubli offers two distinct loop modes that determine how videos and timeframes are played:
              </p>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-300 mb-3">Full + Timeframes Mode</h3>
                  <p className="text-sm mb-3">
                    Videos play from start to finish, but when they reach timeframe sections,
                    those sections loop according to their individual counters before continuing.
                  </p>
                  <div className="space-y-2 text-xs">
                    <div className="bg-dark-800/50 rounded p-2">
                      <strong>Example:</strong> Video plays 0:00-1:30, then loops timeframe 1:30-2:00 (3x),
                      continues 2:00-3:45, loops timeframe 3:45-4:15 (2x), then finishes 4:15-end.
                    </div>
                    <div className="text-blue-200">
                      <strong>Best for:</strong> Music videos where you want to hear the full song but emphasize certain parts
                    </div>
                  </div>
                </div>

                <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-purple-300 mb-3">Timeframes Only Mode</h3>
                  <p className="text-sm mb-3">
                    Only the defined timeframes are played in sequence, each looping according to
                    its individual counter. The rest of the video is skipped.
                  </p>
                  <div className="space-y-2 text-xs">
                    <div className="bg-dark-800/50 rounded p-2">
                      <strong>Example:</strong> Plays timeframe 1:30-2:00 (3x), then timeframe 3:45-4:15 (2x),
                      then moves to next video or repeats based on video loop counter.
                    </div>
                    <div className="text-purple-200">
                      <strong>Best for:</strong> Study materials, tutorials, or when you only want specific sections
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-dark-800/50 rounded-lg p-4">
                <h3 className="font-semibold text-white mb-2">Loop Counters Hierarchy:</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li><strong>Timeframe Counters:</strong> How many times each timeframe loops</li>
                  <li><strong>Video Counter:</strong> How many times the entire video (with its timeframes) loops</li>
                  <li><strong>Queue Counter:</strong> How many times the entire queue loops (default: infinite)</li>
                </ol>
              </div>
            </div>
          </section>

          {/* Timeframes */}
          <section id="timeframes" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">6. Timeframes</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                Timeframes allow you to define specific sections within videos that should be looped.
                Each timeframe has its own loop counter and can be previewed independently.
              </p>

              <div className="space-y-4">
                <div className="bg-dark-800/50 rounded-lg p-4">
                  <h3 className="font-semibold text-white mb-2">Creating Timeframes:</h3>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Add a video to your draft queue</li>
                    <li>Click "Add Timeframe" on the video</li>
                    <li>Set start and end times (in MM:SS format)</li>
                    <li>Configure how many times it should loop</li>
                    <li>Use the preview to test your timeframe</li>
                  </ol>
                </div>

                <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-green-300 mb-2">Timeframe Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Individual loop counters for each timeframe</li>
                    <li>Real-time preview without affecting main playback</li>
                    <li>Visual highlighting during timeframe playback</li>
                    <li>Easy editing and deletion</li>
                    <li>Multiple timeframes per video supported</li>
                  </ul>
                </div>

                <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-yellow-300 mb-2">Best Practices:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Keep timeframes focused on specific sections (chorus, solo, etc.)</li>
                    <li>Use preview to ensure smooth looping points</li>
                    <li>Consider fade-in/fade-out when setting boundaries</li>
                    <li>Test different loop counts to find the right emphasis</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* Queue Management */}
          <section id="queue-management" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">7. Queue Management</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                Queues are collections of videos with their associated loop settings and timeframes.
                You can create, edit, and organize multiple queues.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-dark-800/50 rounded-lg p-4">
                  <h3 className="font-semibold text-white mb-2">Creating Queues:</h3>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Search and add videos to draft</li>
                    <li>Configure loop modes and timeframes</li>
                    <li>Set queue title and description</li>
                    <li>Choose public or private visibility</li>
                    <li>Click "Create Queue" to save</li>
                  </ol>
                </div>

                <div className="bg-dark-800/50 rounded-lg p-4">
                  <h3 className="font-semibold text-white mb-2">Managing Queues:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>View all queues in "My Queues" section</li>
                    <li>Edit existing queues anytime</li>
                    <li>Drag and drop to reorder videos</li>
                    <li>Delete queues you no longer need</li>
                    <li>Toggle public/private visibility</li>
                  </ul>
                </div>
              </div>

              <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                <h3 className="font-semibold text-blue-300 mb-2">Queue Settings:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <strong>Queue Loop Count:</strong><br/>
                    How many times the entire queue repeats (default: infinite)
                  </div>
                  <div>
                    <strong>Video Loop Count:</strong><br/>
                    How many times each video plays before moving to next
                  </div>
                  <div>
                    <strong>Loop Mode:</strong><br/>
                    Full+Timeframes or Timeframes Only
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Player Controls */}
          <section id="player-controls" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">8. Player Controls</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                The player provides comprehensive controls for managing playback and queue navigation.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-dark-800/50 rounded-lg p-4">
                  <h3 className="font-semibold text-white mb-2">Basic Controls:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li><strong>Play/Pause:</strong> Start or stop video playback</li>
                    <li><strong>Previous/Next:</strong> Navigate between queue videos</li>
                    <li><strong>Volume:</strong> Adjust playback volume with slider</li>
                    <li><strong>Mute:</strong> Quickly mute/unmute audio</li>
                  </ul>
                </div>

                <div className="bg-dark-800/50 rounded-lg p-4">
                  <h3 className="font-semibold text-white mb-2">Advanced Features:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li><strong>Queue Loop Counter:</strong> Set how many times queue repeats</li>
                    <li><strong>Shuffle:</strong> Randomize video playback order</li>
                    <li><strong>Current Playing Info:</strong> See what's currently playing</li>
                    <li><strong>Timeframe Display:</strong> Visual indicator during timeframe loops</li>
                  </ul>
                </div>
              </div>

              <div className="bg-primary-900/20 border border-primary-500/30 rounded-lg p-4">
                <h3 className="font-semibold text-primary-300 mb-2">Player Interface:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Header controls for compact view</li>
                  <li>Full player view with queue display</li>
                  <li>Minimize/maximize toggle for space saving</li>
                  <li>Mobile-responsive design</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Sharing */}
          <section id="sharing" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">9. Sharing Queues</h2>
            <div className="space-y-4 text-dark-200">
              <p>
                Share your queues with others through public links or browse queues created by the community.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-green-300 mb-2">Making Queues Public:</h3>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Create or edit a queue</li>
                    <li>Toggle "Make Public" option</li>
                    <li>Save the queue</li>
                    <li>Share the generated link</li>
                    <li>Others can load and play your queue</li>
                  </ol>
                </div>

                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-300 mb-2">Browsing Public Queues:</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Visit "Public Queues" section</li>
                    <li>Browse community-created queues</li>
                    <li>Load queues directly to your player</li>
                    <li>See queue metadata and video count</li>
                    <li>Discover new content and ideas</li>
                  </ul>
                </div>
              </div>

              <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
                <h3 className="font-semibold text-yellow-300 mb-2">Privacy Options:</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Private Queues:</strong><br/>
                    Only visible to you, stored in your account
                  </div>
                  <div>
                    <strong>Public Queues:</strong><br/>
                    Visible to everyone, can be shared via link
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Tips and Best Practices */}
          <section id="tips" className="glassmorphism rounded-2xl p-6">
            <h2 className="text-2xl font-semibold mb-4 text-primary-400">10. Tips & Best Practices</h2>
            <div className="space-y-4 text-dark-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
                    <h3 className="font-semibold text-green-300 mb-2">For Music Lovers:</h3>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Use "Full + Timeframes" mode for complete songs</li>
                      <li>Create timeframes for choruses or solos</li>
                      <li>Set video loop count to 2-3 for favorite songs</li>
                      <li>Use shuffle for variety in long sessions</li>
                    </ul>
                  </div>

                  <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-300 mb-2">For Learning:</h3>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Use "Timeframes Only" for specific concepts</li>
                      <li>Set high loop counts for difficult sections</li>
                      <li>Create focused queues by topic</li>
                      <li>Use preview to find exact learning moments</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4">
                    <h3 className="font-semibold text-purple-300 mb-2">Performance Tips:</h3>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Keep timeframes under 2 minutes for smooth looping</li>
                      <li>Test timeframes with preview before saving</li>
                      <li>Use descriptive queue titles for organization</li>
                      <li>Minimize player when not actively watching</li>
                    </ul>
                  </div>

                  <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-4">
                    <h3 className="font-semibold text-orange-300 mb-2">Organization:</h3>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      <li>Create separate queues for different moods/activities</li>
                      <li>Use tags when creating public queues</li>
                      <li>Regularly clean up old queues</li>
                      <li>Upgrade to permanent account for sync</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-dark-800/50 rounded-lg p-4 mt-6">
                <h3 className="font-semibold text-white mb-2">Need Help?</h3>
                <p className="text-sm">
                  If you encounter any issues or have questions about using Tubli, check the status indicator
                  in the header (green for authenticated, yellow for anonymous) and ensure you have a stable
                  internet connection for the best experience.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </MainLayout>
  )
}

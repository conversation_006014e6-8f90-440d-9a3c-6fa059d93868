'use client'

import { useI18n } from '@/hooks/useI18n'

/**
 * Demo component to showcase i18n functionality
 * This can be temporarily added to any page to test translations
 */
export function I18nDemo() {
  const { t, language, setLanguage, availableLanguages } = useI18n()

  return (
    <div className="bg-dark-800/50 rounded-2xl p-6 border border-dark-700/50 mb-6">
      <h2 className="text-xl font-bold text-white mb-4">🌍 i18n System Demo</h2>
      
      <div className="space-y-4">
        {/* Current Language */}
        <div>
          <strong className="text-white">Current Language:</strong> 
          <span className="ml-2 text-primary-400">{language}</span>
        </div>

        {/* Available Languages */}
        <div>
          <strong className="text-white">Available Languages:</strong>
          <div className="flex gap-2 mt-2">
            {availableLanguages.map(lang => (
              <button
                key={lang.code}
                onClick={() => setLanguage(lang.code)}
                className={`px-3 py-1 rounded text-sm ${
                  language === lang.code 
                    ? 'bg-primary-600 text-white' 
                    : 'bg-dark-700 text-dark-200 hover:bg-dark-600'
                }`}
              >
                {lang.flag} {lang.name}
              </button>
            ))}
          </div>
        </div>

        {/* Translation Examples */}
        <div>
          <strong className="text-white">Translation Examples ({language === 'en' ? 'English' : 'Español'}):</strong>
          <div className="mt-2 space-y-2 text-sm">
            <div>
              <code className="text-primary-400">t('common.loading')</code> →
              <span className="ml-2 text-dark-200">"{t('common.loading')}"</span>
            </div>
            <div>
              <code className="text-primary-400">t('settings.title')</code> →
              <span className="ml-2 text-dark-200">"{t('settings.title')}"</span>
            </div>
            <div>
              <code className="text-primary-400">t('navigation.createQueue')</code> →
              <span className="ml-2 text-dark-200">"{t('navigation.createQueue')}"</span>
            </div>
            <div>
              <code className="text-primary-400">t('navigation.magicQueue')</code> →
              <span className="ml-2 text-dark-200">"{t('navigation.magicQueue')}"</span>
            </div>
            <div>
              <code className="text-primary-400">t('queue.emptyQueue')</code> →
              <span className="ml-2 text-dark-200">"{t('queue.emptyQueue')}"</span>
            </div>
            <div>
              <code className="text-primary-400">t('time.minutesAgo', {`{count: 5}`})</code> →
              <span className="ml-2 text-dark-200">"{t('time.minutesAgo', { count: 5 })}"</span>
            </div>
            <div>
              <code className="text-primary-400">t('time.hoursAgo', {`{count: 2}`})</code> →
              <span className="ml-2 text-dark-200">"{t('time.hoursAgo', { count: 2 })}"</span>
            </div>
            <div>
              <code className="text-primary-400">t('missing.key')</code> →
              <span className="ml-2 text-red-400">"{t('missing.key')}"</span>
              <span className="ml-2 text-xs text-dark-400">(fallback to key)</span>
            </div>
          </div>
        </div>

        {/* Language Comparison */}
        <div>
          <strong className="text-white">Language Comparison:</strong>
          <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium text-primary-400 mb-2">🇺🇸 English</div>
              <div className="space-y-1 text-dark-200">
                <div>"Create Queue"</div>
                <div>"Account Settings"</div>
                <div>"Loading..."</div>
                <div>"5 minutes ago"</div>
              </div>
            </div>
            <div>
              <div className="font-medium text-primary-400 mb-2">🇪🇸 Español</div>
              <div className="space-y-1 text-dark-200">
                <div>"Crear Cola"</div>
                <div>"Configuración de Cuenta"</div>
                <div>"Cargando..."</div>
                <div>"Hace 5 minutos"</div>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-dark-700/30 rounded-lg p-3 text-sm text-dark-300">
          <p className="font-medium text-white mb-2">How to use in components:</p>
          <pre className="text-xs text-dark-400 overflow-x-auto">
{`import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { t } = useI18n()

  return (
    <div>
      <h1>{t('settings.title')}</h1>
      <p>{t('queue.emptyQueue')}</p>
      <button>{t('common.save')}</button>
    </div>
  )
}`}
          </pre>
        </div>
      </div>
    </div>
  )
}

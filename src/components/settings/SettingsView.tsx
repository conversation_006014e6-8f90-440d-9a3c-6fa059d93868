'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/components/providers/ToastProvider'
import { useI18n } from '@/hooks/useI18n'
import { AccountDeletionSection } from './AccountDeletionSection'
import { LanguageSelector } from './LanguageSelector'

export function SettingsView() {
  const { user, updateProfile } = useAuth()
  const { showToast } = useToast()
  const { t } = useI18n()
  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false)
  const [profileForm, setProfileForm] = useState({
    displayName: user?.displayName || '',
    email: user?.email || ''
  })

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setIsUpdatingProfile(true)
    try {
      await updateProfile({
        displayName: profileForm.displayName
      })
      showToast(t('settings.profileUpdated'), 'success')
    } catch (error: any) {
      console.error('Failed to update profile:', error)
      showToast(error.message || t('settings.updateFailed'), 'error')
    } finally {
      setIsUpdatingProfile(false)
    }
  }

  if (!user || user.isAnonymous) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-dark-700/50 flex items-center justify-center">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <h2 className="text-xl font-bold text-white mb-2">{t('settings.signInRequired')}</h2>
          <p className="text-dark-300">
            {t('settings.signInRequiredDescription')}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">{t('settings.title')}</h1>
        <p className="text-dark-300">
          {t('settings.description')}
        </p>
      </div>

      {/* Profile Information */}
      <div className="bg-dark-800/50 rounded-2xl p-6 border border-dark-700/50">
        <h2 className="text-xl font-bold text-white mb-6">{t('settings.profileInformation')}</h2>
        
        <form onSubmit={handleProfileUpdate} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Display Name */}
            <div>
              <label htmlFor="displayName" className="block text-sm font-medium text-dark-200 mb-2">
                {t('settings.displayName')}
              </label>
              <input
                type="text"
                id="displayName"
                value={profileForm.displayName}
                onChange={(e) => setProfileForm(prev => ({ ...prev, displayName: e.target.value }))}
                className="w-full px-4 py-3 bg-dark-700/50 border border-dark-600/50 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Your display name"
              />
            </div>

            {/* Email (read-only) */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-dark-200 mb-2">
                {t('settings.emailAddress')}
              </label>
              <input
                type="email"
                id="email"
                value={profileForm.email}
                disabled
                className="w-full px-4 py-3 bg-dark-700/30 border border-dark-600/30 rounded-lg text-dark-300 cursor-not-allowed"
              />
              <p className="text-xs text-dark-400 mt-1">
                {t('settings.emailCannotChange')}
              </p>
            </div>
          </div>

          {/* Account Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-dark-700/50">
            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('settings.accountCreated')}
              </label>
              <div className="text-dark-300">
                {new Date(user.createdAt).toLocaleDateString()}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('settings.lastSignIn')}
              </label>
              <div className="text-dark-300">
                {new Date(user.lastLoginAt).toLocaleDateString()}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('settings.accountType')}
              </label>
              <div className="text-dark-300">
                {user.email ? t('settings.emailAccount') : t('settings.googleAccount')}
              </div>
            </div>
          </div>

          {/* Update Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isUpdatingProfile || profileForm.displayName === user.displayName}
              className="px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-dark-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-all"
            >
              {isUpdatingProfile ? t('settings.updating') : t('settings.updateProfile')}
            </button>
          </div>
        </form>
      </div>

      {/* Language Settings */}
      <LanguageSelector />

      {/* Account Deletion Section */}
      <AccountDeletionSection />
    </div>
  )
}

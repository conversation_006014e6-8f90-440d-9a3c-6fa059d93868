# Hugo build output
public/
resources/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Node modules (if using npm for build tools)
node_modules/

# Environment files
.env
.env.local
.idea

# API Configuration files (contain sensitive API keys)
config.json
**/config.json
projects/**/config.json

# Firebase Configuration files (contain sensitive Firebase credentials)
firebase-config.json
**/firebase-config.json
projects/**/firebase-config.json
out
.next

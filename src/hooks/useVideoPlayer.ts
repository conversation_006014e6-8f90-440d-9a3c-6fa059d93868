'use client'

import { createContext, useContext } from 'react'

export interface VideoPlayerContextType {
  currentTimeframeIndex: number
  currentTimeframeLoopCount: number
  currentTime: number
  isInTimeframeMode: boolean
}

export const VideoPlayerContext = createContext<VideoPlayerContextType>({
  currentTimeframeIndex: 0,
  currentTimeframeLoopCount: 0,
  currentTime: 0,
  isInTimeframeMode: false,
})

export const useVideoPlayer = () => {
  const context = useContext(VideoPlayerContext)
  if (!context) {
    throw new Error('useVideoPlayer must be used within a VideoPlayerProvider')
  }
  return context
}

// Authentication-related types and interfaces

export interface User {
  uid: string
  email: string | null
  displayName: string | null
  photoURL: string | null
  emailVerified: boolean
  isAnonymous: boolean
  createdAt: number
  lastLoginAt: number
  preferences?: UserPreferences
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  autoplay: boolean
  defaultVolume: number
  showNotifications: boolean
  defaultQueuePrivacy: 'private' | 'public'
  language: string
  timezone: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface SignInCredentials {
  email: string
  password: string
}

export interface SignUpCredentials {
  email: string
  password: string
  displayName: string
}

export interface AuthError {
  code: string
  message: string
}

// Firebase Auth error codes
export const AUTH_ERROR_CODES = {
  EMAIL_ALREADY_IN_USE: 'auth/email-already-in-use',
  INVALID_EMAIL: 'auth/invalid-email',
  OPERATION_NOT_ALLOWED: 'auth/operation-not-allowed',
  WEAK_PASSWORD: 'auth/weak-password',
  USER_DISABLED: 'auth/user-disabled',
  USER_NOT_FOUND: 'auth/user-not-found',
  WRONG_PASSWORD: 'auth/wrong-password',
  TOO_MANY_REQUESTS: 'auth/too-many-requests',
  NETWORK_REQUEST_FAILED: 'auth/network-request-failed',
  POPUP_CLOSED_BY_USER: 'auth/popup-closed-by-user',
  CANCELLED_POPUP_REQUEST: 'auth/cancelled-popup-request',
  POPUP_BLOCKED: 'auth/popup-blocked',
  CREDENTIAL_ALREADY_IN_USE: 'auth/credential-already-in-use',
} as const

export type AuthErrorCode = typeof AUTH_ERROR_CODES[keyof typeof AUTH_ERROR_CODES]

// Auth provider types
export type AuthProvider = 'google' | 'email' | 'anonymous'

export interface AuthProviderConfig {
  google: {
    enabled: boolean
    scopes?: string[]
  }
  email: {
    enabled: boolean
    requireEmailVerification?: boolean
  }
  anonymous: {
    enabled: boolean
  }
}

// Session management
export interface UserSession {
  uid: string
  sessionId: string
  createdAt: number
  lastActivity: number
  deviceInfo?: {
    userAgent: string
    platform: string
    browser: string
  }
}

// User profile update
export interface UserProfileUpdate {
  displayName?: string
  photoURL?: string
  preferences?: Partial<UserPreferences>
}

// Password reset
export interface PasswordResetRequest {
  email: string
}

// Email verification
export interface EmailVerificationRequest {
  user: User
}

// Account deletion
export interface AccountDeletionRequest {
  user: User
  reason?: string
}

'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { formatTime } from '@/lib/utils/time'
import { debounce } from '@/lib/utils/format'

interface RangeSliderProps {
  min: number
  max: number
  startValue: number
  endValue: number
  onChange: (start: number, end: number) => void
  step?: number
  className?: string
  disabled?: boolean
}

export function RangeSlider({
  min,
  max,
  startValue,
  endValue,
  onChange,
  step = 1,
  className = '',
  disabled = false
}: RangeSliderProps) {
  const [isDragging, setIsDragging] = useState<'start' | 'end' | null>(null)
  const [localStart, setLocalStart] = useState(startValue)
  const [localEnd, setLocalEnd] = useState(endValue)
  const sliderRef = useRef<HTMLDivElement>(null)

  // Update local state when props change
  useEffect(() => {
    setLocalStart(startValue)
    setLocalEnd(endValue)
  }, [startValue, endValue])

  // Ensure values are within bounds
  const clampedStart = Math.max(min, Math.min(max - step, localStart))
  const clampedEnd = Math.max(clampedStart + step, Math.min(max, localEnd))

  // Debounced onChange to reduce performance impact
  const debouncedOnChange = useCallback(
    debounce((start: number, end: number) => {
      onChange(start, end)
    }, 100),
    [onChange]
  )

  // Update local state and trigger debounced onChange
  const updateValues = useCallback((newStart: number, newEnd: number) => {
    setLocalStart(newStart)
    setLocalEnd(newEnd)
    debouncedOnChange(newStart, newEnd)
  }, [debouncedOnChange])

  const getValueFromPosition = useCallback((clientX: number) => {
    if (!sliderRef.current) return min

    const rect = sliderRef.current.getBoundingClientRect()
    const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
    const value = min + percentage * (max - min)

    // Round to nearest step
    return Math.round(value / step) * step
  }, [min, max, step])

  const handlePointerDown = useCallback((handle: 'start' | 'end') => (e: React.PointerEvent) => {
    if (disabled) return
    e.preventDefault()
    setIsDragging(handle)
    // Capture pointer for better touch handling
    e.currentTarget.setPointerCapture(e.pointerId)
  }, [disabled])

  // Throttled pointer move for smooth dragging
  const throttledPointerMove = useCallback(
    debounce((clientX: number) => {
      if (!isDragging || disabled) return

      const newValue = getValueFromPosition(clientX)

      if (isDragging === 'start') {
        const newStart = Math.min(newValue, clampedEnd - step)
        updateValues(newStart, clampedEnd)
      } else {
        const newEnd = Math.max(newValue, clampedStart + step)
        updateValues(clampedStart, newEnd)
      }
    }, 16), // ~60fps
    [isDragging, disabled, getValueFromPosition, clampedStart, clampedEnd, step, updateValues]
  )

  const handlePointerMove = useCallback((e: PointerEvent) => {
    if (!isDragging || disabled) return

    // For immediate visual feedback, update local state directly
    const newValue = getValueFromPosition(e.clientX)

    if (isDragging === 'start') {
      const newStart = Math.min(newValue, clampedEnd - step)
      setLocalStart(newStart)
    } else {
      const newEnd = Math.max(newValue, clampedStart + step)
      setLocalEnd(newEnd)
    }

    // Throttled update for performance
    throttledPointerMove(e.clientX)
  }, [isDragging, disabled, getValueFromPosition, clampedStart, clampedEnd, step, throttledPointerMove])

  const handlePointerUp = useCallback(() => {
    setIsDragging(null)
  }, [])

  // Handle pointer events (works for both mouse and touch)
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('pointermove', handlePointerMove)
      document.addEventListener('pointerup', handlePointerUp)
      return () => {
        document.removeEventListener('pointermove', handlePointerMove)
        document.removeEventListener('pointerup', handlePointerUp)
      }
    }
  }, [isDragging, handlePointerMove, handlePointerUp])

  // Calculate positions as percentages
  const startPercentage = ((clampedStart - min) / (max - min)) * 100
  const endPercentage = ((clampedEnd - min) / (max - min)) * 100

  const adjustStartTime = useCallback((delta: number) => {
    if (disabled) return
    const newStart = Math.max(min, Math.min(clampedEnd - step, clampedStart + delta))
    updateValues(newStart, clampedEnd)
  }, [disabled, min, clampedEnd, step, clampedStart, updateValues])

  const adjustEndTime = useCallback((delta: number) => {
    if (disabled) return
    const newEnd = Math.max(clampedStart + step, Math.min(max, clampedEnd + delta))
    updateValues(clampedStart, newEnd)
  }, [disabled, max, clampedStart, step, clampedEnd, updateValues])

  return (
    <div className={`relative ${className}`}>
      {/* Time labels */}
      <div className="flex justify-between text-xs text-dark-400 mb-3">
        <span>{formatTime(min)}</span>
        <span>{formatTime(max)}</span>
      </div>

      {/* Active dragging indicator */}
      {isDragging && (
        <div className="absolute -top-1 left-0 right-0 text-center">
          <div className="inline-block bg-primary-500 text-white text-xs px-2 py-1 rounded-full shadow-lg">
            Adjusting {isDragging} time
          </div>
        </div>
      )}

      {/* Slider track */}
      <div
        ref={sliderRef}
        className={`relative h-10 bg-dark-700 rounded-xl cursor-pointer transition-all hover:bg-dark-600 ${
          disabled ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        onPointerDown={(e) => {
          if (disabled) return
          const newValue = getValueFromPosition(e.clientX)
          const startDistance = Math.abs(newValue - clampedStart)
          const endDistance = Math.abs(newValue - clampedEnd)

          if (startDistance < endDistance) {
            const newStart = Math.min(newValue, clampedEnd - step)
            updateValues(newStart, clampedEnd)
          } else {
            const newEnd = Math.max(newValue, clampedStart + step)
            updateValues(clampedStart, newEnd)
          }
        }}
      >
        {/* Selected range */}
        <div
          className="absolute top-0 h-full bg-primary-500/40 rounded-xl transition-all"
          style={{
            left: `${startPercentage}%`,
            width: `${endPercentage - startPercentage}%`
          }}
        />

        {/* Start handle */}
        <div
          className={`absolute top-1/2 w-6 h-6 bg-primary-500 border-3 border-white rounded-full transform -translate-y-1/2 cursor-grab shadow-xl transition-all touch-none ${
            isDragging === 'start' ? 'scale-125 cursor-grabbing shadow-2xl ring-4 ring-primary-500/30' : 'hover:scale-110 hover:shadow-2xl'
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          style={{ left: `calc(${startPercentage}% - 12px)` }}
          onPointerDown={handlePointerDown('start')}
        />

        {/* End handle */}
        <div
          className={`absolute top-1/2 w-6 h-6 bg-primary-500 border-3 border-white rounded-full transform -translate-y-1/2 cursor-grab shadow-xl transition-all touch-none ${
            isDragging === 'end' ? 'scale-125 cursor-grabbing shadow-2xl ring-4 ring-primary-500/30' : 'hover:scale-110 hover:shadow-2xl'
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          style={{ left: `calc(${endPercentage}% - 12px)` }}
          onPointerDown={handlePointerDown('end')}
        />
      </div>

      {/* Current values with precision controls */}
      <div className="flex justify-between items-center text-sm mt-4">
        <div className="flex items-center gap-3">
          <div className="text-primary-300">
            <span className="text-dark-400">Start: </span>
            <span className="font-mono font-semibold">{formatTime(clampedStart)}</span>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={() => adjustStartTime(-step)}
              disabled={disabled || clampedStart <= min}
              className={`w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold transition-all ${
                disabled || clampedStart <= min
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-105 active:scale-95'
              }`}
              title="Decrease start time by 1 second"
            >
              −
            </button>
            <button
              onClick={() => adjustStartTime(step)}
              disabled={disabled || clampedStart >= clampedEnd - step}
              className={`w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold transition-all ${
                disabled || clampedStart >= clampedEnd - step
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-105 active:scale-95'
              }`}
              title="Increase start time by 1 second"
            >
              +
            </button>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1">
            <button
              onClick={() => adjustEndTime(-step)}
              disabled={disabled || clampedEnd <= clampedStart + step}
              className={`w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold transition-all ${
                disabled || clampedEnd <= clampedStart + step
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-105 active:scale-95'
              }`}
              title="Decrease end time by 1 second"
            >
              −
            </button>
            <button
              onClick={() => adjustEndTime(step)}
              disabled={disabled || clampedEnd >= max}
              className={`w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold transition-all ${
                disabled || clampedEnd >= max
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white hover:scale-105 active:scale-95'
              }`}
              title="Increase end time by 1 second"
            >
              +
            </button>
          </div>
          <div className="text-primary-300">
            <span className="text-dark-400">End: </span>
            <span className="font-mono font-semibold">{formatTime(clampedEnd)}</span>
          </div>
        </div>
      </div>

      {/* Duration and progress indicators */}
      <div className="flex justify-between items-center mt-4">
        <div className="text-xs text-dark-400 bg-dark-700/50 px-3 py-1 rounded-full">
          Duration: <span className="text-primary-300 font-mono font-semibold">{formatTime(clampedEnd - clampedStart)}</span>
        </div>
        <div className="text-xs text-dark-400">
          {((clampedEnd - clampedStart) / (max - min) * 100).toFixed(1)}% of video
        </div>
      </div>
    </div>
  )
}

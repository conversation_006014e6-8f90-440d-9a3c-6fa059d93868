'use client'

import { useState, useRef, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useNavigation } from '@/hooks/useNavigation'
import { useToast } from '@/components/providers/ToastProvider'
import { useI18n } from '@/hooks/useI18n'
import { AuthModal } from './AuthModal'

export function AuthButton() {
  const { user, isAuthenticated, signInWithGoogle, signOut } = useAuth()
  const { setActiveView } = useNavigation()
  const { showToast } = useToast()
  const { t } = useI18n()
  const [isLoading, setIsLoading] = useState(false)
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleGoogleSignIn = async () => {
    const wasAnonymous = user?.isAnonymous || false
    setIsLoading(true)
    try {
      const result = await signInWithGoogle()
      if (result) {
        if (wasAnonymous) {
          showToast('Account linked successfully! Your data has been saved.', 'success', 4000)
        } else {
          showToast('Welcome back!', 'success')
        }
      }
    } catch (error) {
      console.error('Sign in error:', error)
      showToast('Failed to sign in. Please try again.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    setIsLoading(true)
    setShowDropdown(false)
    try {
      await signOut()
      showToast('Signed out successfully', 'info')
    } catch (error) {
      console.error('Sign out error:', error)
      showToast('Failed to sign out. Please try again.', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSettingsClick = () => {
    setActiveView('settings')
    setShowDropdown(false)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDropdown])

  // Only show authenticated UI for non-anonymous users
  if (isAuthenticated && user && !user.isAnonymous) {
    return (
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setShowDropdown(!showDropdown)}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-dark-700/50 transition-all"
        >
          {user.photoURL && (
            <img
              src={user.photoURL}
              alt={user.displayName || 'User'}
              className="w-8 h-8 rounded-full border-2 border-primary-500"
            />
          )}
          <span className="text-sm font-medium text-white hidden md:block">
            {user.displayName || user.email}
          </span>
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="currentColor"
            className={`text-dark-400 transition-transform ${showDropdown ? 'rotate-180' : ''}`}
          >
            <path d="M7 10l5 5 5-5z"/>
          </svg>
        </button>

        {/* Dropdown Menu */}
        {showDropdown && (
          <div className="absolute right-0 top-full mt-2 w-48 bg-dark-800 rounded-lg border border-dark-700/50 shadow-xl z-50">
            <div className="py-2">
              <button
                onClick={handleSettingsClick}
                className="w-full px-4 py-2 text-left text-sm text-white hover:bg-dark-700/50 transition-all flex items-center space-x-2"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                  <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                </svg>
                <span>{t('buttons.settings')}</span>
              </button>

              <div className="border-t border-dark-700/50 my-1"></div>

              <button
                onClick={handleSignOut}
                disabled={isLoading}
                className="w-full px-4 py-2 text-left text-sm text-white hover:bg-dark-700/50 transition-all flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                  <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                </svg>
                <span>{isLoading ? t('status.signingOut') : t('buttons.signOut')}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    )
  }

  // Check if user is anonymous
  const isAnonymousUser = user?.isAnonymous || false

  return (
    <>
      <div className="flex items-center space-x-2">
        {/* Google Sign In Button */}
        <button
          onClick={handleGoogleSignIn}
          disabled={isLoading}
          className="btn-primary text-sm px-3 py-2 flex items-center space-x-2"
          title={isAnonymousUser ? t('tooltips.saveDataGoogle') : t('tooltips.signInGoogle')}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          <span className="hidden sm:inline">{isLoading ? t('status.signingIn') : t('buttons.google')}</span>
        </button>

        {/* Email Sign In Button */}
        <button
          onClick={() => setShowAuthModal(true)}
          className="btn-secondary text-sm px-3 py-2 flex items-center space-x-2"
          title={isAnonymousUser ? t('tooltips.saveDataEmail') : t('tooltips.signInEmail')}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
          </svg>
          <span className="hidden sm:inline">{t('buttons.email')}</span>
        </button>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </>
  )
}

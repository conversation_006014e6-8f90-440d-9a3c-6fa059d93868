#!/usr/bin/env node

/**
 * Environment Variables Checker
 * Validates that all required environment variables are present
 */

const fs = require('fs');
const path = require('path');

// Required environment variables
const requiredEnvVars = [
  'NEXT_PUBLIC_YOUTUBE_API_KEY',
  'NEXT_PUBLIC_FIREBASE_API_KEY',
  'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
  'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
  'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
  'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
  'NEXT_PUBLIC_FIREBASE_APP_ID',
];

// Optional environment variables
const optionalEnvVars = [
  'NEXT_PUBLIC_APP_ENV',
  'NEXT_PUBLIC_DEBUG_MODE',
  'NEXT_PUBLIC_API_BASE_URL',
  'NEXT_PUBLIC_GOOGLE_ANALYTICS_ID',
  'NEXT_PUBLIC_SENTRY_DSN',
];

function checkEnvironment() {
  console.log('🔍 Checking environment variables...\n');

  const environment = process.env.NODE_ENV || 'development';
  console.log(`📦 Environment: ${environment}`);
  
  // Check if .env.local exists for development
  const envLocalPath = path.join(process.cwd(), '.env.local');
  const envExamplePath = path.join(process.cwd(), '.env.example');
  
  if (environment === 'development' && !fs.existsSync(envLocalPath)) {
    console.log('❌ .env.local file not found');
    if (fs.existsSync(envExamplePath)) {
      console.log('💡 Run: npm run env:copy to create .env.local from template');
    }
    process.exit(1);
  }

  // Check required variables
  const missingRequired = [];
  const presentRequired = [];
  
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      presentRequired.push(envVar);
    } else {
      missingRequired.push(envVar);
    }
  });

  // Check optional variables
  const presentOptional = [];
  const missingOptional = [];
  
  optionalEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      presentOptional.push(envVar);
    } else {
      missingOptional.push(envVar);
    }
  });

  // Report results
  console.log('\n✅ Required variables present:');
  presentRequired.forEach(envVar => {
    const value = process.env[envVar];
    const maskedValue = value.length > 10 ? 
      `${value.substring(0, 6)}...${value.substring(value.length - 4)}` : 
      '***';
    console.log(`   ${envVar}: ${maskedValue}`);
  });

  if (missingRequired.length > 0) {
    console.log('\n❌ Missing required variables:');
    missingRequired.forEach(envVar => {
      console.log(`   ${envVar}`);
    });
  }

  if (presentOptional.length > 0) {
    console.log('\n🔧 Optional variables present:');
    presentOptional.forEach(envVar => {
      console.log(`   ${envVar}: ${process.env[envVar]}`);
    });
  }

  if (missingOptional.length > 0) {
    console.log('\n⚠️  Optional variables not set:');
    missingOptional.forEach(envVar => {
      console.log(`   ${envVar}`);
    });
  }

  // Exit with error if required variables are missing
  if (missingRequired.length > 0) {
    console.log('\n❌ Build cannot proceed with missing required variables');
    console.log('📝 Please check your .env.local file or environment configuration');
    process.exit(1);
  }

  console.log('\n✅ Environment check passed!');
}

// Run the check
checkEnvironment();

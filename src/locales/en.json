{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "search": "Search", "clear": "Clear", "refresh": "Refresh", "share": "Share", "copy": "Copy", "paste": "Paste", "add": "Add", "remove": "Remove", "play": "Play", "pause": "Pause", "stop": "Stop", "repeat": "Repeat", "shuffle": "Shuffle", "volume": "Volume", "settings": "Settings", "profile": "Profile", "account": "Account", "signIn": "Sign In", "signOut": "Sign Out", "signUp": "Sign Up", "language": "Language", "warning": "Warning", "note": "Note", "with": "with"}, "navigation": {"quickPlay": "Quick Play", "createQueue": "Create Queue", "magicQueue": "Magic Queue", "myQueues": "My Queues", "publicQueues": "Public Queues"}, "search": {"title": "Add YouTube Videos", "description": "Find and add videos to your queue", "editTitle": "Edit Queue - Add Videos", "editDescription": "Find and add more videos to your queue", "placeholder": "Search for videos, artists, or songs...", "noResults": "No Results Found", "noResultsDescription": "No videos found for \"{query}\". Try a different search term or add videos manually.", "tryManualInput": "Try Manual Link Input", "addVideos": "Add Videos", "searchMode": "Search Mode", "manualMode": "Manual Mode", "pasteYouTubeLink": "Paste YouTube link here (e.g., https://youtube.com/watch?v=...)", "noResultsFound": "No Results Found", "noVideosFoundFor": "No videos found for \"{query}\". Try a different search term or add videos manually."}, "quickPlay": {"title": "Quick Play", "description": "Instantly play and loop any YouTube video without saving to queues", "urlPlaceholder": "Paste YouTube URL here (e.g., https://youtube.com/watch?v=...)", "playButton": "Play", "noVideoLoaded": "No video loaded", "enterYouTubeUrl": "Enter a YouTube URL above to start playing", "enableLoop": "Enable infinite loop", "disableLoop": "Disable loop", "clearVideo": "Clear video", "infoTitle": "Quick Play Features", "infoPoint1": "Instant playback without creating queues", "infoPoint2": "Simple infinite loop toggle", "infoPoint3": "No data persistence or authentication required", "featuresTitle": "Why use Quick Play?", "feature1": "No queue management needed", "feature2": "Instant YouTube URL playback", "feature3": "Simple infinite loop control", "feature4": "Perfect for single video loops", "supportedFormats": "Supported formats:"}, "queue": {"currentQueue": "Current Queue", "emptyQueue": "Your queue is empty", "emptyQueueDescription": "Add videos to start playing!", "addVideosToStart": "Add videos to start playing!", "queueLoops": "Queue loops:", "infinite": "∞ Infinite", "clearQueue": "Clear Queue", "shareQueue": "Share Queue", "loadQueue": "Load <PERSON>", "saveQueue": "Save Queue", "deleteQueue": "Delete Queue", "editQueue": "<PERSON>", "queueTitle": "Queue Title", "queueDescription": "Queue Description", "videoCount": "Videos", "totalDuration": "Total Duration", "createdAt": "Created", "updatedAt": "Updated", "privacy": "Privacy", "private": "Private", "public": "Public"}, "magic": {"title": "Magic Queue Generator", "description": "Describe what you want to watch and let AI create the perfect queue for you", "editTitle": "Edit Queue - Magic Recommendations", "editDescription": "Get AI-powered video recommendations to enhance your queue", "placeholder": "Describe what kind of videos you'd like to watch...", "generating": "Generating...", "examples": {"educational": "Educational videos about Programming", "teachMe": "Teach me something new", "relaxing": "Relaxing nature documentaries", "guitar": "Beginner guitar tutorials", "history": "History of ancient civilizations", "cooking": "Cooking healthy meals", "space": "Space exploration documentaries", "language": "Learn a new language"}}, "settings": {"title": "Account <PERSON><PERSON>", "description": "Manage your profile and account preferences", "profileInformation": "Profile Information", "displayName": "Display Name", "emailAddress": "Email Address", "emailCannotChange": "Email cannot be changed. Contact support if needed.", "accountCreated": "Account Created", "lastSignIn": "Last Sign In", "accountType": "Account Type", "emailAccount": "<PERSON><PERSON> Account", "googleAccount": "Google Account", "updateProfile": "Update Profile", "updating": "Updating...", "profileUpdated": "Profile updated successfully", "updateFailed": "Failed to update profile", "signInRequired": "Sign In Required", "signInRequiredDescription": "You need to sign in with an account to access settings.", "languageSettings": "Language Settings", "selectLanguage": "Select Language", "languageChanged": "Language changed successfully"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "displayName": "Display Name", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "haveAccount": "Already have an account?", "noAccount": "Don't have an account?", "rememberPassword": "Remember your password?", "dontHaveAccount": "Don't have an account?", "signingIn": "Signing in...", "creatingAccount": "Creating account...", "sendingEmail": "Sending email...", "sendResetEmail": "Send Reset Email", "signInWithGoogle": "Sign in with Google", "continueAsGuest": "Continue as Guest", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters", "passwordsDoNotMatch": "Passwords do not match", "invalidEmail": "Invalid email address", "userNotFound": "User not found", "wrongPassword": "Incorrect password", "emailAlreadyInUse": "Email is already in use", "weakPassword": "Password is too weak", "tooManyRequests": "Too many requests. Please try again later.", "networkError": "Network error. Please check your connection.", "unknownError": "An unknown error occurred"}, "player": {"loading": "Loading video...", "error": "Error loading video", "noVideo": "No video selected", "selectVideo": "Select a video from your queue to start playing", "currentlyPlaying": "Currently Playing", "nextUp": "Next Up", "timeframe": "Timeframe", "startTime": "Start Time", "endTime": "End Time", "loopCount": "Loop Count", "editTimeframe": "Edit Timeframe", "addTimeframe": "Add Timeframe", "removeTimeframe": "Remove Timeframe"}, "controls": {"play": "Play", "pause": "Pause", "stop": "Stop", "next": "Next", "previous": "Previous", "volume": "Volume", "mute": "Mute", "unmute": "Unmute", "loop": "Loop", "shuffle": "Shuffle"}, "errors": {"generic": "Something went wrong. Please try again.", "networkError": "Network error. Please check your internet connection.", "notFound": "The requested resource was not found.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access to this resource is forbidden.", "serverError": "Server error. Please try again later.", "validationError": "Please check your input and try again.", "timeoutError": "Request timed out. Please try again.", "youtubeApiLimit": "YouTube API Limit Reached", "quotaExceeded": "The YouTube search quota has been exceeded. You can still add videos manually using direct links.", "accountDeletionWarning": "Once you delete your account, there is no going back. This action cannot be undone.", "securityNoticeText": "For your protection, you may need to sign out and sign back in before deleting your account if you haven't signed in recently.", "deletionWarning": "You are about to permanently delete your account", "thisWillRemove": "This will immediately remove all your data including {queueCount} queue{queuePlural}{videoText}, loops, and preferences. This action cannot be undone.", "withVideos": " with {videoCount} video{videoPlural}"}, "time": {"justNow": "Just now", "minuteAgo": "1 minute ago", "minutesAgo": "{count} minutes ago", "hourAgo": "1 hour ago", "hoursAgo": "{count} hours ago", "dayAgo": "1 day ago", "daysAgo": "{count} days ago", "weekAgo": "1 week ago", "weeksAgo": "{count} weeks ago", "monthAgo": "1 month ago", "monthsAgo": "{count} months ago", "yearAgo": "1 year ago", "yearsAgo": "{count} years ago"}, "app": {"title": "<PERSON><PERSON><PERSON> - <PERSON> Looper", "description": "A powerful YouTube video queue and looper tool with real-time search functionality using YouTube Data API v3, Firebase Firestore for cloud storage, and persistent queue sharing that allows you to find, queue, and loop multiple videos infinitely.", "tagline": "YouTube Queue & Looper", "earlyAccess": "Early Access", "version": "Version 0.1.0", "appDescription": "Create, share, and loop YouTube video queues with advanced timeframe controls. This is an early access version with ongoing development."}, "footer": {"copyright": "All rights reserved", "documentation": "Documentation", "website": "Tubli Website"}, "forms": {"required": "*", "optional": "(optional)", "characters": "characters", "separateWithCommas": "Separate tags with commas", "enterTitle": "Enter a title for your queue...", "enterDescription": "Describe your queue (optional)...", "enterTags": "music, rock, favorites (comma-separated)...", "yourName": "Your name", "yourEmail": "<EMAIL>", "confirmPassword": "Confirm password", "enterPassword": "Enter your access password", "enterQueueTitle": "Enter queue title...", "enterQueueDescription": "Enter queue description...", "searchVideosToAdd": "Search for videos to add...", "searchPublicQueues": "Search public queues...", "yourDisplayName": "Your display name"}, "validation": {"invalidYouTubeUrl": "Invalid YouTube URL", "validYouTubeUrl": "Valid YouTube URL", "endTimeMustBeGreater": "End time must be greater than start time and within video duration", "invalidTimeframe": "Invalid Timeframe", "clearUrl": "Clear URL", "clearSearch": "Clear search"}, "status": {"signingIn": "Signing in...", "signingOut": "Signing out...", "creating": "Creating...", "updating": "Updating...", "saving": "Saving...", "deleting": "Deleting...", "sharing": "Sharing...", "generating": "Generating...", "loadingQueues": "Loading your queues...", "loadingPublicQueues": "Loading public queues...", "loadingVideo": "Loading video...", "verifyingAccess": "Verifying Access...", "creatingMagicQueue": "Creating Magic Queue...", "loadingData": "Loading your data...", "deletingAccount": "Deleting Account...", "savingChanges": "Saving...", "duplicating": "Duplicating..."}, "buttons": {"google": "Google", "email": "Email", "manualLink": "Manual Link", "clearAll": "Clear All", "switchToManualInput": "Switch to Manual Link Input", "tryManualInput": "Try Manual Link Input", "showPreview": "Show", "hidePreview": "<PERSON>de", "addTimeframe": "Add Timeframe", "saveAndExit": "Save & Exit", "backToQueue": "Back to Queue", "enterApplication": "Enter Application", "generateMagicQueue": "Generate Magic Queue", "createQueue": "Create Queue", "updateQueue": "Update Queue", "saveChanges": "Save Changes", "cancelSelection": "Cancel Selection", "bulkActions": "Bulk Actions", "refreshQueues": "Refresh queues", "editQueue": "Edit queue", "duplicateQueue": "Duplicate queue", "deleteQueue": "Delete queue", "copyLink": "Copy link", "deleteAccount": "Delete Account", "deleteMyAccount": "Delete My Account"}, "tooltips": {"saveDataGoogle": "Save your data with Google", "signInGoogle": "Sign in with Google", "saveDataEmail": "Save your data with Email", "signInEmail": "Sign in with <PERSON><PERSON>", "signInToShare": "Sign in to share queues", "shareQueue": "Share this queue", "clearDraftQueue": "Clear draft queue", "editVideoSettings": "Edit video loop settings and timeframes", "removeFromDraft": "Remove from draft queue", "restartQueue": "Restart queue with original settings", "editTimeframe": "Edit timeframe", "deleteTimeframe": "Delete timeframe", "close": "Close"}, "messages": {"noVideosInDraft": "No videos in draft queue", "searchAndAddVideos": "Search and add videos to build your queue!", "noVideoSelected": "No video selected", "addVideosToQueue": "Add videos to your queue to start playing", "unknownChannel": "Unknown Channel", "untitledQueue": "Untitled Queue", "noQueuesYet": "No personal queues yet", "createFirstQueue": "Create your first queue to get started", "signInRequired": "Sign in required", "signInToManageQueues": "Sign in to create and manage your personal queues", "noPublicQueues": "No public queues available", "checkBackLater": "Check back later for shared queues from the community", "noQueuesFound": "No queues found", "tryDifferentSearch": "Try a different search term", "noVideosInQueue": "No videos in this queue", "searchAndAddAbove": "Search and add videos above", "secureAccessRequired": "Secure access required for early testing phase", "developmentMode": "Development Mode • Password stored in Firebase"}, "labels": {"queueTitle": "Queue Title", "description": "Description", "tags": "Tags", "queueLoopCount": "Queue Loop Count", "makePublic": "Make Public", "makePrivate": "Make Private", "allowDiscovery": "Allow others to discover and play your queue", "accessPassword": "Access Password", "confirmDeletion": "To confirm deletion, type", "below": "below", "dataToBeDeleted": "Data to be deleted", "securityNotice": "Security Notice", "dangerZone": "Danger Zone", "whatWillBeDeleted": "What will be deleted", "totalQueues": "total queues", "totalVideos": "total videos", "publicQueues": "public queues", "privateQueues": "private queues", "totalQueuesCount": "Total Queues", "publicCount": "Public", "plays": "plays", "videos": "videos", "shareLink": "Share Link", "copied": "Copied!", "public": "Public", "private": "Private", "readyToPlay": "Ready to play", "draftQueue": "Draft Queue", "totalDuration": "Total Duration", "quickInsights": "Quick Insights", "duration": "Duration", "channel": "Channel", "views": "views", "timeframes": "timeframes", "timeframe": "timeframe", "noTimeframesConfigured": "No timeframes configured", "clickEditToAdd": "Click Edit to add timeframes"}, "timeframeEditor": {"videoLoopEditor": "Video Loop Editor", "configureSettings": "Configure video loop settings and manage timeframes", "generalVideoSettings": "General Video Settings", "appliesToEntireVideo": "Applies to entire video", "videoLoopCount": "Video Loop Count", "howManyTimesVideo": "How many times the entire video plays", "totalVideoDuration": "Total video duration", "loopMode": "Loop Mode", "timeframesOnly": "Timeframes Only", "fullVideoPlusTimeframes": "Full Video + Timeframes", "playOnlyTimeframes": "🎯 Play only the selected timeframes in sequence", "playFullThenTimeframes": "🎬 Play the full video first, then loop through timeframes", "configureTimeframe": "Now configure individual timeframe", "timeframeManagement": "Timeframe Management", "existingTimeframes": "existing timeframes", "existingTimeframesList": "Existing Timeframes", "timeRangeSelection": "Time Range Selection", "timeframeLoopCount": "Timeframe Loop Count", "howManyTimesTimeframe": "How many times this timeframe loops", "timeframeDuration": "Timeframe Duration", "timeframePreview": "Timeframe Preview", "clickToPreview": "Click \"Show\" to preview this timeframe", "keyboardShortcuts": "Keyboard Shortcuts", "playbackSummary": "Playback <PERSON><PERSON><PERSON>", "videoSettings": "Video Settings", "loops": "Loops", "times": "times", "time": "time", "mode": "Mode", "total": "Total", "combinedDuration": "Combined duration", "videoPlays": "Video plays", "estimatedDuration": "Est. duration"}, "queueCreation": {"creatingNewQueue": "Creating New Queue", "addVideosToCreate": "Add videos to create your new queue", "editQueue": "<PERSON>", "updateQueueDetails": "Update your queue details and videos", "saveWithTitle": "Save your draft queue with a title and description", "infiniteLoopForever": "Infinite (loop forever)", "specificNumber": "Specific number", "queueWillLoop": "Queue will loop infinitely until manually stopped", "queueWillPlayTimes": "Queue will play {count} time{plural} then stop", "creatingMagicQueue": "Creating Magic Queue", "letAICurate": "Let AI curate the perfect video queue for you"}, "magicQueue": {"generator": "Magic Queue Generator", "editQueueRecommendations": "Edit Queue - Magic Recommendations", "description": "Describe what you want to watch and let AI create the perfect queue for you", "editDescription": "Get AI-powered video recommendations to enhance your queue", "numberOfVideos": "Number of videos", "maxDurationOptional": "Max duration (optional)", "filterByDuration": "Filter by duration", "tryTheseExamples": "Try these examples:", "upTo5Minutes": "Up to 5 minutes", "upTo10Minutes": "Up to 10 minutes", "upTo20Minutes": "Up to 20 minutes", "upTo30Minutes": "Up to 30 minutes", "upTo1Hour": "Up to 1 hour", "5Videos": "5 videos", "10Videos": "10 videos", "15Videos": "15 videos", "20Videos": "20 videos"}, "accountDeletion": {"profileInformation": "Your profile information and preferences", "privateQueues": "All your private queues and video loops", "publicQueuesData": "All your public queues and shared content", "authenticationData": "Your account authentication and login access", "associatedData": "All associated data and settings", "complianceNote": "This deletion complies with data privacy regulations. All data will be permanently removed from our servers and cannot be recovered."}, "personalQueues": {"myQueues": "My Queues", "description": "Create, manage, and share your personal video queues"}, "statistics": {"totalQueues": "Total Queues", "totalVideos": "Total Videos", "totalDuration": "Total Duration", "totalViews": "Total Views", "averageQueueLength": "Average Queue Length", "recentQueues": "Recent Queues", "thisWeek": "This Week", "quickInsights": "Quick Insights", "youHaveQueues": "You have {count} queue{plural} with {videos} video{videoPlural}", "publicQueuesShareable": "{count} of your queues are public and shareable", "totalWatchTime": "Total watch time: {duration}", "createdThisWeek": "You created {count} queue{plural} this week"}, "publicQueues": {"title": "Public Queues", "description": "Discover and play shared video queues"}}
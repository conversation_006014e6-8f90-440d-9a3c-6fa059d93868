// Firebase-related utility functions

/**
 * Remove undefined values from an object recursively
 * Firebase doesn't allow undefined values in documents
 */
export function removeUndefinedValues(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }
  
  if (Array.isArray(obj)) {
    return obj.map(removeUndefinedValues).filter(item => item !== undefined)
  }
  
  if (typeof obj === 'object') {
    const cleaned: any = {}
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key] = removeUndefinedValues(value)
      }
    }
    return cleaned
  }
  
  return obj
}

/**
 * Prepare data for Firebase by removing undefined values and converting types
 */
export function prepareForFirebase(data: any): any {
  return removeUndefinedValues(data)
}

/**
 * Validate that an object doesn't contain undefined values
 */
export function validateFirebaseData(obj: any, path: string = 'root'): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  function checkValue(value: any, currentPath: string) {
    if (value === undefined) {
      errors.push(`Undefined value found at ${currentPath}`)
      return
    }

    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        checkValue(item, `${currentPath}[${index}]`)
      })
    } else if (value !== null && typeof value === 'object') {
      Object.entries(value).forEach(([key, val]) => {
        checkValue(val, `${currentPath}.${key}`)
      })
    }
  }

  checkValue(obj, path)

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Convert various timestamp formats to milliseconds
 * Handles Firestore Timestamps, Date objects, and number timestamps
 */
export function timestampToMillis(timestamp: any): number {
  if (!timestamp) {
    return Date.now()
  }

  // If it's already a number (milliseconds), return it
  if (typeof timestamp === 'number') {
    return timestamp
  }

  // Handle Firebase serverTimestamp() placeholder
  if (timestamp && typeof timestamp === 'object' && timestamp._methodName === 'serverTimestamp') {
    // This is a serverTimestamp() placeholder, use current time
    return Date.now()
  }

  // If it's a Firestore Timestamp with toMillis method
  if (timestamp && typeof timestamp.toMillis === 'function') {
    try {
      return timestamp.toMillis()
    } catch (error) {
      console.warn('Failed to convert Firestore timestamp:', error)
      return Date.now()
    }
  }

  // If it's a Date object
  if (timestamp instanceof Date) {
    return timestamp.getTime()
  }

  // If it has seconds and nanoseconds (Firestore Timestamp structure)
  if (timestamp && typeof timestamp.seconds === 'number') {
    return timestamp.seconds * 1000 + (timestamp.nanoseconds || 0) / 1000000
  }

  // If it's a string that can be parsed as a date
  if (typeof timestamp === 'string') {
    const parsed = Date.parse(timestamp)
    if (!isNaN(parsed)) {
      return parsed
    }
  }

  // Only warn for truly unknown formats, not serverTimestamp placeholders
  if (!(timestamp && typeof timestamp === 'object' && timestamp._methodName)) {
    console.warn('Unknown timestamp format, using current time:', timestamp)
  }
  return Date.now()
}

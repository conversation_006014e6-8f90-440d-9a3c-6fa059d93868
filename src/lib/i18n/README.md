# Internationalization (i18n) System

This directory contains the internationalization system for the YouTube Looper application.

## Overview

The i18n system provides:
- **Multiple language support** with English as the default
- **Type-safe translations** with TypeScript support
- **Local storage persistence** for language preferences
- **Immediate language switching** without page refresh
- **Fallback handling** to English for missing translations
- **Interpolation support** for dynamic values in translations

## Architecture

### Core Files

- **`types.ts`** - TypeScript interfaces and type definitions
- **`utils.ts`** - Utility functions for loading and processing translations
- **`index.ts`** - Main module exports

### Components

- **`I18nProvider.tsx`** - React context provider for i18n state
- **`LanguageSelector.tsx`** - UI component for language selection

### Translation Files

- **`src/locales/en.json`** - English translations (default)
- **`src/locales/[lang].json`** - Additional language files (future)

## Usage

### Basic Translation

```tsx
import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { t } = useI18n()
  
  return (
    <div>
      <h1>{t('common.loading')}</h1>
      <p>{t('settings.description')}</p>
    </div>
  )
}
```

### Translation with Interpolation

```tsx
const { t } = useI18n()

// For "5 minutes ago"
const timeText = t('time.minutesAgo', { count: 5 })

// For custom interpolation
const welcomeText = t('welcome.message', { name: 'John' })
```

### Language Management

```tsx
import { useLanguage } from '@/hooks/useI18n'

function LanguageSettings() {
  const { language, setLanguage, availableLanguages } = useLanguage()
  
  return (
    <select 
      value={language} 
      onChange={(e) => setLanguage(e.target.value)}
    >
      {availableLanguages.map(lang => (
        <option key={lang.code} value={lang.code}>
          {lang.name}
        </option>
      ))}
    </select>
  )
}
```

## Adding New Languages

1. **Create translation file**: Add `src/locales/[lang-code].json`
2. **Update types**: Add language code to `SupportedLanguage` type
3. **Update configuration**: Add language info to `SUPPORTED_LANGUAGES` array
4. **Update validation**: Update `isValidLanguage` function

### Example: Adding Spanish

1. Create `src/locales/es.json`:
```json
{
  "common": {
    "loading": "Cargando...",
    "save": "Guardar"
  }
}
```

2. Update `types.ts`:
```typescript
export type SupportedLanguage = 'en' | 'es'

export const SUPPORTED_LANGUAGES: LanguageInfo[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' }
]
```

3. Update `utils.ts`:
```typescript
export function isValidLanguage(language: string): boolean {
  return ['en', 'es'].includes(language)
}
```

## Translation Key Structure

Translation keys use dot notation for nested organization:

```
common.loading          → "Loading..."
settings.title          → "Account Settings"
time.minutesAgo         → "{count} minutes ago"
navigation.createQueue  → "Create Queue"
```

## Best Practices

### 1. Consistent Key Naming
- Use descriptive, hierarchical keys
- Group related translations under common prefixes
- Use camelCase for key names

### 2. Interpolation
- Use `{variableName}` for dynamic values
- Keep interpolation simple and clear
- Provide fallback values when possible

### 3. Fallback Handling
- Always provide English translations as fallback
- Handle missing keys gracefully
- Log warnings for missing translations in development

### 4. Performance
- Translations are loaded asynchronously
- Only the current language is loaded (not all languages)
- Fallback language is loaded only when needed

## Testing

The i18n system includes comprehensive tests:

```bash
npm test -- src/__tests__/i18n/
```

Tests cover:
- Translation loading and fallback
- Key interpolation
- Language switching
- Local storage persistence
- Error handling

## Integration

The i18n system is integrated into the app through:

1. **Provider Setup** in `AppProviders.tsx`
2. **Layout Integration** for dynamic `lang` attribute
3. **Component Usage** throughout the application
4. **Settings Interface** for language selection

## Migration Guide

To migrate existing hardcoded strings:

1. **Identify text strings** in components
2. **Add translation keys** to `en.json`
3. **Replace strings** with `t('key')` calls
4. **Add i18n hook** to components
5. **Test translation** functionality

Example migration:
```tsx
// Before
<h1>Account Settings</h1>

// After
const { t } = useI18n()
<h1>{t('settings.title')}</h1>
```

// Time-related utility functions

/**
 * Format seconds to MM:SS format
 */
export function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

/**
 * Parse MM:SS format to seconds
 */
export function parseTime(timeString: string): number {
  const parts = timeString.split(':')
  if (parts.length !== 2) return 0
  
  const mins = parseInt(parts[0]) || 0
  const secs = parseInt(parts[1]) || 0
  
  return mins * 60 + secs
}

/**
 * Validate timeframe for a video
 */
export function validateTimeframe(startTime?: number, endTime?: number, duration?: number): {
  isValid: boolean
  error?: string
} {
  if (startTime !== undefined && startTime < 0) {
    return { isValid: false, error: 'Start time cannot be negative' }
  }
  
  if (endTime !== undefined && endTime <= 0) {
    return { isValid: false, error: 'End time must be positive' }
  }
  
  if (duration !== undefined) {
    if (startTime !== undefined && startTime >= duration) {
      return { isValid: false, error: 'Start time cannot be greater than video duration' }
    }
    
    if (endTime !== undefined && endTime > duration) {
      return { isValid: false, error: 'End time cannot be greater than video duration' }
    }
  }
  
  if (startTime !== undefined && endTime !== undefined && startTime >= endTime) {
    return { isValid: false, error: 'Start time must be less than end time' }
  }
  
  return { isValid: true }
}

/**
 * Calculate effective duration based on timeframe
 */
export function getEffectiveDuration(duration: number, startTime?: number, endTime?: number): number {
  const start = startTime || 0
  const end = endTime || duration
  return Math.max(0, end - start)
}

/**
 * Calculate total duration for a draft item including loop count (legacy)
 */
export function getTotalDraftDuration(duration: number, loopCount: number, startTime?: number, endTime?: number): number {
  const effectiveDuration = getEffectiveDuration(duration, startTime, endTime)
  return effectiveDuration * loopCount
}

/**
 * Calculate total duration for a draft item with new timeframe structure
 */
export function getTotalDraftDurationNew(draftItem: import('@/lib/types/video').DraftVideoItem): number {
  const { duration, timeframes, loopSettings } = draftItem

  if (timeframes.length === 0) {
    // No timeframes, use whole video
    return duration * loopSettings.videoLoopCount
  }

  if (loopSettings.loopMode === 'timeframes-only') {
    // Only play timeframes
    const timeframesTotalDuration = timeframes.reduce((total, timeframe) => {
      const timeframeDuration = timeframe.endTime - timeframe.startTime
      return total + (timeframeDuration * timeframe.loopCount)
    }, 0)
    return timeframesTotalDuration * loopSettings.videoLoopCount
  } else {
    // Play whole video + timeframes
    const wholeVideoDuration = duration
    const timeframesTotalDuration = timeframes.reduce((total, timeframe) => {
      const timeframeDuration = timeframe.endTime - timeframe.startTime
      return total + (timeframeDuration * timeframe.loopCount)
    }, 0)
    return (wholeVideoDuration + timeframesTotalDuration) * loopSettings.videoLoopCount
  }
}

'use client'

import { <PERSON>actN<PERSON>, useState, useCallback } from 'react'
import { QuickPlayContext, QuickPlayState } from '@/hooks/useQuickPlay'
import { youtubeService } from '@/lib/services/youtube'
import { VideoMetadata } from '@/lib/types/video'

interface QuickPlayProviderProps {
  children: ReactNode
}

export function QuickPlayProvider({ children }: QuickPlayProviderProps) {
  const [state, setState] = useState<QuickPlayState>({
    currentVideo: null,
    isPlaying: false,
    isLooping: true, // Default to infinite loop
    volume: 1,
    isLoading: false,
    error: null,
  })

  const setPlaying = useCallback((isPlaying: boolean) => {
    setState(prev => ({ ...prev, isPlaying }))
  }, [])

  const setLoading = useCallback((isLoading: boolean) => {
    setState(prev => ({ ...prev, isLoading }))
  }, [])

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }))
  }, [])

  const loadVideo = useCallback(async (url: string) => {
    setLoading(true)
    setError(null)

    try {
      // Validate YouTube URL
      if (!youtubeService.isValidYouTubeUrl(url)) {
        throw new Error('Invalid YouTube URL')
      }

      // Extract video ID
      const videoId = youtubeService.extractVideoId(url)
      if (!videoId) {
        throw new Error('Could not extract video ID from URL')
      }

      // Get video metadata
      const metadata = await youtubeService.getVideoMetadata(videoId)
      if (!metadata) {
        throw new Error('Could not fetch video metadata')
      }

      setState(prev => ({
        ...prev,
        currentVideo: metadata,
        isPlaying: true, // Auto-play when loaded
        error: null,
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load video'
      setError(errorMessage)
      console.error('Quick play load error:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const clearVideo = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentVideo: null,
      isPlaying: false,
      error: null,
    }))
  }, [])

  const play = useCallback(() => {
    setPlaying(true)
  }, [setPlaying])

  const pause = useCallback(() => {
    setPlaying(false)
  }, [setPlaying])

  const setVolume = useCallback((volume: number) => {
    setState(prev => ({ ...prev, volume: Math.max(0, Math.min(1, volume)) }))
  }, [])

  const toggleLoop = useCallback(() => {
    setState(prev => ({ ...prev, isLooping: !prev.isLooping }))
  }, [])

  const contextValue = {
    ...state,
    loadVideo,
    clearVideo,
    play,
    pause,
    setVolume,
    toggleLoop,
    setPlaying,
    setLoading,
    setError,
  }

  return (
    <QuickPlayContext.Provider value={contextValue}>
      {children}
    </QuickPlayContext.Provider>
  )
}

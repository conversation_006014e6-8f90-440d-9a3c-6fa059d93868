/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { I18nProvider } from '@/components/providers/I18nProvider'
import { useI18n } from '@/hooks/useI18n'
import { loadTranslations, isValidLanguage } from '@/lib/i18n/utils'
import { SUPPORTED_LANGUAGES } from '@/lib/i18n/types'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Test component that uses i18n
function TestComponent() {
  const { t, language, setLanguage, availableLanguages, isLoading } = useI18n()

  if (isLoading) {
    return <div data-testid="loading">Loading...</div>
  }

  return (
    <div>
      <div data-testid="current-language">{language}</div>
      <div data-testid="translated-text">{t('common.loading')}</div>
      <div data-testid="nested-translation">{t('settings.title')}</div>
      <div data-testid="interpolated-text">{t('time.minutesAgo', { count: 5 })}</div>
      <div data-testid="missing-key">{t('nonexistent.key')}</div>
      <div data-testid="available-languages">{availableLanguages.length}</div>
      <button 
        data-testid="change-language" 
        onClick={() => setLanguage('to')}
      >
        Change to Tongan
      </button>
    </div>
  )
}

describe('Tongan Language Support', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
  })

  describe('Language Configuration', () => {
    it('should include Tongan in supported languages', () => {
      const tonganLanguage = SUPPORTED_LANGUAGES.find(lang => lang.code === 'to')
      
      expect(tonganLanguage).toBeDefined()
      expect(tonganLanguage?.name).toBe('Tongan')
      expect(tonganLanguage?.nativeName).toBe('lea fakatonga')
      expect(tonganLanguage?.flag).toBe('🇹🇴')
    })

    it('should validate Tongan as a valid language', () => {
      expect(isValidLanguage('to')).toBe(true)
      expect(isValidLanguage('invalid')).toBe(false)
    })

    it('should have 3 supported languages including Tongan', () => {
      expect(SUPPORTED_LANGUAGES).toHaveLength(3)
      const languageCodes = SUPPORTED_LANGUAGES.map(lang => lang.code)
      expect(languageCodes).toContain('en')
      expect(languageCodes).toContain('es')
      expect(languageCodes).toContain('to')
    })
  })

  describe('Tongan Translation Loading', () => {
    it('should load Tongan translations correctly', async () => {
      const tonganTranslations = await loadTranslations('to')
      
      expect(tonganTranslations).toBeDefined()
      expect(tonganTranslations.common).toBeDefined()
      expect(tonganTranslations.common.loading).toBe('Loading...')
      expect(tonganTranslations.common.save).toBe('Save')
      expect(tonganTranslations.navigation.createQueue).toBe('Create Queue')
      expect(tonganTranslations.settings.title).toBe('Account Settings')
    })

    it('should contain translation metadata', async () => {
      const tonganTranslations = await loadTranslations('to')
      
      expect(tonganTranslations._note).toContain('TONGAN LANGUAGE FILE')
      expect(tonganTranslations._translationStatus).toContain('Professional translation services are needed')
      expect(tonganTranslations._basicTonganPhrases).toBeDefined()
      expect(tonganTranslations._basicTonganPhrases.hello).toBe('Mālō e lelei')
      expect(tonganTranslations._basicTonganPhrases.thankYou).toBe('Mālō \'aupito')
    })

    it('should have all required translation sections', async () => {
      const tonganTranslations = await loadTranslations('to')
      
      const requiredSections = [
        'common', 'navigation', 'search', 'queue', 'magic', 'settings',
        'auth', 'player', 'errors', 'time', 'app', 'footer'
      ]

      requiredSections.forEach(section => {
        expect(tonganTranslations[section]).toBeDefined()
      })
    })
  })

  describe('Tongan Language Integration', () => {
    it('should render with Tongan language support', async () => {
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Check that 3 languages are available (including Tongan)
      expect(screen.getByTestId('available-languages')).toHaveTextContent('3')
    })

    it('should switch to Tongan language', async () => {
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Initially should be English
      expect(screen.getByTestId('current-language')).toHaveTextContent('en')

      // Click to change to Tongan
      const changeButton = screen.getByTestId('change-language')
      changeButton.click()

      // Wait for language change
      await waitFor(() => {
        expect(screen.getByTestId('current-language')).toHaveTextContent('to')
      })

      // Verify localStorage was called to store the language
      expect(localStorageMock.setItem).toHaveBeenCalledWith('youtube-looper-language', 'to')
    })

    it('should handle interpolation in Tongan', async () => {
      const tonganTranslations = await loadTranslations('to')
      
      // Test interpolation works with Tongan translations
      expect(tonganTranslations.time.minutesAgo).toBe('{count} minutes ago')
      
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Check interpolated text
      expect(screen.getByTestId('interpolated-text')).toHaveTextContent('5 minutes ago')
    })

    it('should fallback to English for missing translations', async () => {
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Missing key should return the key itself
      expect(screen.getByTestId('missing-key')).toHaveTextContent('nonexistent.key')
    })
  })

  describe('Tongan Language Persistence', () => {
    it('should persist Tongan language selection', async () => {
      // Mock localStorage to return Tongan
      localStorageMock.getItem.mockReturnValue('to')

      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Should start with Tongan language
      expect(screen.getByTestId('current-language')).toHaveTextContent('to')
    })
  })

  describe('Tongan Language Metadata', () => {
    it('should provide guidance for professional translation', async () => {
      const tonganTranslations = await loadTranslations('to')
      
      expect(tonganTranslations._note).toContain('PROFESSIONAL TRANSLATION NEEDED')
      expect(tonganTranslations._translationStatus).toContain('English fallbacks and basic Tongan phrases')
      expect(tonganTranslations._translationStatus).toContain('Professional translation services are needed')
    })

    it('should include basic Tongan phrases for reference', async () => {
      const tonganTranslations = await loadTranslations('to')
      const basicPhrases = tonganTranslations._basicTonganPhrases
      
      expect(basicPhrases.hello).toBe('Mālō e lelei')
      expect(basicPhrases.thankYou).toBe('Mālō \'aupito')
      expect(basicPhrases.yes).toBe('\'Io')
      expect(basicPhrases.no).toBe('Ikai')
      expect(basicPhrases.please).toBe('Kātaki / Fakamolemole')
      expect(basicPhrases.goodbye).toBe('\'Alu ā / Nofo ā')
      expect(basicPhrases.withLove).toBe('\'Ofa atu')
      expect(basicPhrases.howAreYou).toBe('Fefe hake?')
      expect(basicPhrases.fine).toBe('Sai pe')
      expect(basicPhrases.food).toBe('Kai')
      expect(basicPhrases.drink).toBe('Inu')
      expect(basicPhrases.house).toBe('Fale')
    })
  })
})

'use client'

import { useEffect } from 'react'
import { useI18n } from '@/hooks/useI18n'

/**
 * Component to update the HTML lang attribute when language changes
 * This ensures the document language attribute stays in sync with the user's preference
 */
export function LanguageHtmlAttribute() {
  const { language } = useI18n()

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.lang = language
    }
  }, [language])

  // This component doesn't render anything
  return null
}

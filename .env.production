# PRODUCTION ENVIRONMENT
# This file contains production environment variables
# Update these values for your production deployment

# Environment
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production

# YouTube API Configuration
# Get from: https://console.cloud.google.com/
# 1. Create project, enable YouTube Data API v3, create API key
NEXT_PUBLIC_YOUTUBE_API_KEY=AIzaSyBIkKeuqIJ1YT6cO3TCokYwH-j2RyNgfVA

# Firebase Configuration (Production)
# Get from: https://console.firebase.google.com/
# 1. Create project, add web app, copy config values
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBwiMIosGBOKAgg6vuNlAfikkyGG8sgp6c
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=auth.tubli.to
NEXT_PUBLIC_FIREBASE_PROJECT_ID=looper-2630d
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=looper-2630d.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=193989658637
NEXT_PUBLIC_FIREBASE_APP_ID=1:193989658637:web:1e9aa8226a5236c14c6d44

# Development specific settings
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_API_BASE_URL=http://tubli.to

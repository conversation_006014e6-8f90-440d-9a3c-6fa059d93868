'use client'

import { ReactNode } from 'react'
import { FirebaseProvider } from './FirebaseProvider'
import { AuthProvider } from './AuthProvider'
import { QueueProvider } from './QueueProvider'
import { DraftQueueProvider } from './DraftQueueProvider'
import { QuickPlayProvider } from './QuickPlayProvider'
import { NavigationProvider } from './NavigationProvider'
import { YouTubeProvider } from './YouTubeProvider'
import { ToastProvider } from './ToastProvider'
import { I18nProvider } from './I18nProvider'
import { AppWrapper } from './AppWrapper'
import { PasswordGateProvider } from './PasswordGateProvider'
import { PasswordGate } from '@/components/auth/PasswordGate'
import { LanguageHtmlAttribute } from './LanguageHtmlAttribute'
import { usePasswordGate } from '@/hooks/usePasswordGate'

interface AppProvidersProps {
  children: ReactNode
}

// Component to conditionally render app content based on password verification
function AppContent({ children }: { children: ReactNode }) {
  const { isPasswordVerified } = usePasswordGate()

  if (!isPasswordVerified) {
    return <PasswordGate />
  }

  return (
    <I18nProvider>
      <LanguageHtmlAttribute />
      <FirebaseProvider>
        <AuthProvider>
          <AppWrapper>
            <YouTubeProvider>
              <QueueProvider>
                <DraftQueueProvider>
                  <QuickPlayProvider>
                    <NavigationProvider>
                      <ToastProvider>
                        {children}
                      </ToastProvider>
                    </NavigationProvider>
                  </QuickPlayProvider>
                </DraftQueueProvider>
              </QueueProvider>
            </YouTubeProvider>
          </AppWrapper>
        </AuthProvider>
      </FirebaseProvider>
    </I18nProvider>
  )
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <PasswordGateProvider>
      <AppContent>
        {children}
      </AppContent>
    </PasswordGateProvider>
  )
}

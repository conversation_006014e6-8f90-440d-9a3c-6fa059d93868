'use client'

import { createContext, useContext } from 'react'
import { I18nContextType, TranslationValues } from '@/lib/i18n/types'

// Create the i18n context
export const I18nContext = createContext<I18nContextType | null>(null)

/**
 * Hook to access internationalization functionality
 */
export function useI18n() {
  const context = useContext(I18nContext)
  
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider')
  }
  
  return context
}

/**
 * Shorthand hook for translation function only
 */
export function useTranslation() {
  const { t } = useI18n()
  return { t }
}

/**
 * Hook for language management
 */
export function useLanguage() {
  const { language, setLanguage, availableLanguages, isLoading } = useI18n()
  
  return {
    language,
    setLanguage,
    availableLanguages,
    isLoading
  }
}

# Firebase Deployment Guide

This guide provides detailed instructions for deploying Firebase configurations and applications across multiple environments.

## Prerequisites

1. **Firebase CLI installed and authenticated**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

2. **Environment variables configured**
   - `.env.local` for development
   - `.env.production` for production
   - Create `.env.staging` for staging if needed

3. **Firebase projects created**
   - Development project
   - Staging project (optional)
   - Production project

## Initial Setup

### 1. Configure Firebase Projects

```bash
# Navigate to Firebase config directory
cd firebase/config

# Add your Firebase projects
firebase use --add
# Select your development project and alias it as 'development'
# Select your staging project and alias it as 'staging'
# Select your production project and alias it as 'production'
```

### 2. Verify Configuration

Check that your `.firebaserc` file contains all project aliases:
```json
{
  "projects": {
    "default": "looper-2630d",
    "development": "your-dev-project-id",
    "staging": "your-staging-project-id",
    "production": "looper-2630d"
  }
}
```

## Deployment Workflows

### Standard Deployment Process

1. **Development Deployment**
   ```bash
   # Deploy everything to development
   npm run deploy:dev
   
   # Or use the script directly
   node firebase/scripts/deploy-firebase.js development
   ```

2. **Staging Deployment**
   ```bash
   # Deploy everything to staging
   npm run deploy:staging
   
   # Or use the script directly
   node firebase/scripts/deploy-firebase.js staging
   ```

3. **Production Deployment**
   ```bash
   # Deploy everything to production
   npm run deploy:prod
   
   # Or use the script directly
   node firebase/scripts/deploy-firebase.js production
   ```

### Granular Deployments

Deploy specific components only:

```bash
# Deploy only hosting
node firebase/scripts/deploy-firebase.js production hosting

# Deploy only Firestore rules
node firebase/scripts/deploy-firebase.js production rules

# Deploy only Firestore indexes
node firebase/scripts/deploy-firebase.js production indexes

# Deploy only Firestore (rules + indexes)
node firebase/scripts/deploy-firebase.js production firestore
```

### Emergency Deployments

For quick fixes or rollbacks:

```bash
# Deploy only hosting to production (fastest)
npm run firebase:deploy:hosting:prod

# Deploy only rules to production
npm run firebase:deploy:rules:prod
```

## Environment-Specific Configurations

### Development Environment
- **Purpose**: Local development and testing
- **Configuration**: `firebase.development.json`
- **Rules**: `firestore.development.rules` (more permissive)
- **Caching**: Short cache times (1 hour)
- **Features**: Emulator support, debug logging

### Staging Environment
- **Purpose**: Pre-production testing
- **Configuration**: `firebase.staging.json`
- **Rules**: `firestore.rules` (production-like)
- **Caching**: Medium cache times (24 hours)
- **Features**: Production-like but with some debugging

### Production Environment
- **Purpose**: Live application
- **Configuration**: `firebase.production.json`
- **Rules**: `firestore.rules` (strict security)
- **Caching**: Long cache times (1 year for static assets)
- **Features**: Full security, monitoring, analytics

## Deployment Checklist

### Before Deployment
- [ ] Environment variables are set correctly
- [ ] Application builds successfully locally
- [ ] Tests pass
- [ ] Security rules are reviewed
- [ ] Database indexes are optimized

### Development Deployment
- [ ] Deploy to development environment
- [ ] Test core functionality
- [ ] Verify authentication works
- [ ] Check database operations
- [ ] Test with emulators if needed

### Staging Deployment
- [ ] Deploy to staging environment
- [ ] Run full test suite
- [ ] Performance testing
- [ ] Security testing
- [ ] User acceptance testing

### Production Deployment
- [ ] Final review of changes
- [ ] Deploy during low-traffic period
- [ ] Monitor deployment logs
- [ ] Verify application functionality
- [ ] Check error rates and performance
- [ ] Have rollback plan ready

## Rollback Procedures

### Quick Rollback (Hosting Only)
```bash
# Redeploy previous version
git checkout <previous-commit>
npm run deploy:prod hosting
```

### Full Rollback
```bash
# Rollback all Firebase services
git checkout <previous-commit>
npm run deploy:prod
```

### Rules Rollback
```bash
# Rollback only security rules
git checkout <previous-commit> -- firebase/config/firestore.rules
npm run firebase:deploy:rules:prod
```

## Monitoring and Troubleshooting

### Deployment Monitoring
- Check Firebase Console for deployment status
- Monitor application logs for errors
- Watch performance metrics
- Check user feedback and error reports

### Common Issues and Solutions

1. **Build Failures**
   - Check environment variables
   - Verify Node.js version compatibility
   - Clear build cache: `rm -rf .next out`

2. **Permission Errors**
   - Verify Firebase CLI authentication: `firebase login`
   - Check project permissions in Firebase Console
   - Ensure correct project is selected

3. **Rule Deployment Failures**
   - Validate rules syntax
   - Test rules with Firebase emulator
   - Check for breaking changes

4. **Index Deployment Issues**
   - Verify index definitions are valid
   - Check for conflicting indexes
   - Monitor index build progress in console

### Getting Help
- Check Firebase Console logs
- Review deployment script output
- Use Firebase CLI debug mode: `firebase --debug deploy`
- Check Firebase Status page for service issues

## Best Practices

1. **Always deploy to development first**
2. **Use staging for final validation**
3. **Deploy during low-traffic periods**
4. **Monitor deployments closely**
5. **Keep rollback procedures ready**
6. **Test security rules thoroughly**
7. **Optimize database indexes**
8. **Use environment-specific configurations**
9. **Keep deployment logs for troubleshooting**
10. **Document any custom deployment procedures**

'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { useQueue } from '@/hooks/useQueue'
import { useI18n } from '@/hooks/useI18n'
import { YouTubePlayer } from '@/lib/types/video'
import { QueueItem } from '@/lib/types/queue'
import { formatTime } from '@/lib/utils/time'
import { VideoPlayerContext } from '@/hooks/useVideoPlayer'
import { VideoPlayerLoopingManager } from '@/lib/looping/VideoPlayerLoopingManager'
import { TimeframeData, VideoLoopSettings } from '@/lib/looping/LoopingRuleEngine'

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
    youtubePlayer: YouTubePlayer | null
  }
}

export function VideoPlayer() {
  const {
    currentVideo,
    isPlaying,
    nextVideo,
    setPlaying,
    items,
    currentIndex,
    queueLoopCount
  } = useQueue()
  const playerRef = useRef<HTMLDivElement>(null)
  const [player, setPlayer] = useState<YouTubePlayer | null>(null)
  const [isPlayerReady, setIsPlayerReady] = useState(false)
  const [isPlayerInstanceReady, setIsPlayerInstanceReady] = useState(false)
  const { t } = useI18n()

  // Timeframe display state
  const [currentTimeframeIndex, setCurrentTimeframeIndex] = useState(0)
  const [currentTimeframeLoopCount, setCurrentTimeframeLoopCount] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [isInTimeframeMode, setIsInTimeframeMode] = useState(false)

  const timeMonitorRef = useRef<NodeJS.Timeout | null>(null)
  const lastLoadedVideoId = useRef<string | null>(null)
  const isLoadingVideo = useRef<boolean>(false)
  const lastVideoEndTimeRef = useRef<number>(0)
  const playerInstanceRef = useRef<YouTubePlayer | null>(null)
  const currentVideoRef = useRef<QueueItem | null>(null)

  // Initialize looping manager
  const loopingManagerRef = useRef<VideoPlayerLoopingManager | null>(null)

  // Initialize looping manager on first render
  useEffect(() => {
    if (!loopingManagerRef.current) {
      loopingManagerRef.current = new VideoPlayerLoopingManager(
        // onStateUpdate
        (videoId, state) => {
          setCurrentTimeframeIndex(state.currentTimeframeIndex)
          setIsInTimeframeMode(state.isInTimeframeMode)
          setCurrentTime(state.currentTime)
        },
        // onSeekTo
        (time) => {
          const currentPlayer = playerInstanceRef.current
          const currentVideoData = currentVideoRef.current

          console.log(`🎯 [SEEK] Attempting to seek to ${time}s, player available: ${!!currentPlayer}, video available: ${!!currentVideoData}`)
          if (currentPlayer && currentVideoData) {
            try {
              if (time === 0 && typeof currentPlayer.loadVideoById === 'function') {
                // For seeking to beginning (video loops), use loadVideoById for better reliability
                console.log(`🎯 [SEEK] Reloading video ${currentVideoData.id} from beginning for loop`)
                currentPlayer.loadVideoById({
                  videoId: currentVideoData.id,
                  startSeconds: 0
                })
              } else if (typeof currentPlayer.seekTo === 'function') {
                // For other seek operations, use regular seekTo
                console.log(`🎯 [SEEK] Seeking to ${time}s`)
                currentPlayer.seekTo(time, true)
                // Also try to play the video after seeking
                if (typeof currentPlayer.playVideo === 'function') {
                  currentPlayer.playVideo()
                }
              }
            } catch (error) {
              console.error(`❌ [SEEK] Error during seek operation:`, error)
            }
          } else {
            console.error(`❌ [SEEK] Cannot seek - player available: ${!!currentPlayer}, video available: ${!!currentVideoData}`)
          }
        },
        // onVideoComplete - Handle queue progression when video is truly complete
        () => {
          console.log('🎬 [LOOP MANAGER] Video complete - moving to next in queue')
          // Move to next video in queue
          const nextIndex = currentIndex + 1
          if (nextIndex < items.length) {
            console.log(`➡️ Moving to next video at index ${nextIndex}`)
            nextVideo()
          } else {
            // At end of queue - check if we should loop
            const wouldLoopCount = queueLoopCount === -1 ? -1 : Math.max(0, queueLoopCount - 1)
            const shouldLoop = queueLoopCount === -1 || wouldLoopCount > 0

            if (shouldLoop) {
              console.log(`🔄 End of queue reached, looping back to start (remaining loops: ${wouldLoopCount === -1 ? 'infinite' : wouldLoopCount})`)
              nextVideo() // This will trigger the queue loop logic
            } else {
              setPlaying(false)
              console.log('🏁 Queue completed! All loops finished.')
              // TODO: Show completion message to user
            }
          }
        }
      )
    }
  }, [currentIndex, items.length, nextVideo, queueLoopCount, setPlaying])

  // Initialize YouTube API
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.YT) {
      console.log('📺 Loading YouTube IFrame API...')

      // Set up the callback for when API is ready
      window.onYouTubeIframeAPIReady = () => {
        console.log('✅ YouTube IFrame API ready')
        setIsPlayerReady(true)
      }

      // Load the API
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
    } else if (window.YT) {
      setIsPlayerReady(true)
    }
  }, [])

  // Keep refs in sync with current values
  useEffect(() => {
    playerInstanceRef.current = player
  }, [player])

  useEffect(() => {
    currentVideoRef.current = currentVideo
  }, [currentVideo])

  // Create refs for functions to avoid circular dependencies
  const startTimeMonitoringRef = useRef<((playerInstance?: any) => void) | null>(null)
  const handleTimeframeEndedRef = useRef<((activePlayer?: any) => void) | null>(null)

  // Handle when a timeframe ends
  const handleTimeframeEnded = useCallback((activePlayer?: any) => {
    // Use the actual video ID that was loaded, not the current queue video
    const actualVideoId = lastLoadedVideoId.current
    if (!actualVideoId || !loopingManagerRef.current) {
      console.log('❌ No loaded video ID or looping manager when handling timeframe end')
      return
    }

    // Find the video data for the actual video that had the timeframe end
    const endedVideo = items.find(item => item.id === actualVideoId)
    if (!endedVideo) {
      console.log(`❌ Could not find video data for timeframe-ended video: ${actualVideoId}`)
      return
    }

    console.log(`🎬 Timeframe ended for video: ${actualVideoId}`)

    const timeframes: TimeframeData[] = endedVideo.timeframes.map(tf => ({
      id: tf.id,
      startTime: tf.startTime,
      endTime: tf.endTime,
      loopCount: tf.loopCount
    }))

    // Use rule engine to handle timeframe end
    loopingManagerRef.current.handleTimeframeEnd(
      actualVideoId,
      timeframes,
      endedVideo.loopSettings
    )

    // Restart monitoring for the next segment using ref to avoid circular dependency
    if (startTimeMonitoringRef.current) {
      startTimeMonitoringRef.current(activePlayer)
    }
  }, [items])

  // Start time monitoring for videos with timeframes
  const startTimeMonitoring = useCallback((playerInstance?: any) => {
    const activePlayer = playerInstance || player

    if (!currentVideo || !activePlayer) {
      console.log('⏰ [MONITOR] Not starting monitoring - no video or player')
      return
    }

    // Clear any existing monitor
    if (timeMonitorRef.current) {
      clearInterval(timeMonitorRef.current)
    }

    const { timeframes, loopSettings } = currentVideo

    if (timeframes.length === 0) {
      console.log('⏰ [MONITOR] No timeframes to monitor')
      return
    }

    console.log(`⏰ [MONITOR] Starting timeframe monitoring for video ${currentVideo.id} (mode: ${loopSettings.loopMode})`)

    // Monitor playback time every 500ms
    timeMonitorRef.current = setInterval(() => {
      try {
        if (activePlayer && typeof activePlayer.getCurrentTime === 'function' && loopingManagerRef.current) {
          const currentTime = activePlayer.getCurrentTime()

          if (typeof currentTime === 'number' && !isNaN(currentTime)) {
            // Update current time in looping manager
            loopingManagerRef.current.updateCurrentTime(currentVideo.id, currentTime)

            const state = loopingManagerRef.current.getState(currentVideo.id)
            if (!state) return

            // Only process if this monitoring is for the currently loaded video
            const actualVideoId = lastLoadedVideoId.current
            if (actualVideoId !== currentVideo.id) {
              console.log(`⏰ [MONITOR] Skipping monitoring - loaded video ${actualVideoId} != current video ${currentVideo.id}`)
              return
            }

            if (loopSettings.loopMode === 'timeframes-only') {
              // Timeframes-only mode: monitor current timeframe
              if (state.currentTimeframeIndex < timeframes.length) {
                const currentTimeframe = timeframes[state.currentTimeframeIndex]
                if (currentTime >= currentTimeframe.endTime - 0.5) {
                  console.log(`⏰ [MONITOR] Timeframes-only: Reached end of timeframe ${state.currentTimeframeIndex + 1} at ${currentTime}s`)
                  clearInterval(timeMonitorRef.current!)
                  timeMonitorRef.current = null
                  // Use ref to avoid circular dependency
                  if (handleTimeframeEndedRef.current) {
                    handleTimeframeEndedRef.current(activePlayer)
                  }
                }
              }
            } else {
              // Whole-video-plus-timeframes mode: detect when we enter/exit timeframes during normal playback
              const activeTimeframe = timeframes.find(tf =>
                currentTime >= tf.startTime && currentTime < tf.endTime
              )

              if (activeTimeframe) {
                // We're in a timeframe - check if we need to initialize it
                const timeframeIndex = timeframes.findIndex(tf => tf.id === activeTimeframe.id)
                if (!(activeTimeframe.id in state.timeframeLoopCounts)) {
                  // First time entering this timeframe - initialize it
                  console.log(`⏰ [MONITOR] Entering timeframe ${timeframeIndex + 1} at ${currentTime}s`)
                  const updatedState = { ...state }
                  updatedState.currentTimeframeIndex = timeframeIndex
                  updatedState.timeframeLoopCounts[activeTimeframe.id] = 0
                  ;(loopingManagerRef.current as any).loopingStates.set(actualVideoId, updatedState)
                }

                // Check if we've reached the end of this timeframe
                if (currentTime >= activeTimeframe.endTime - 0.5) {
                  console.log(`⏰ [MONITOR] Whole-video: Reached end of timeframe ${timeframeIndex + 1} at ${currentTime}s`)
                  clearInterval(timeMonitorRef.current!)
                  timeMonitorRef.current = null
                  // Use ref to avoid circular dependency
                  if (handleTimeframeEndedRef.current) {
                    handleTimeframeEndedRef.current(activePlayer)
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error('❌ Error monitoring timeframe:', error)
      }
    }, 500)
  }, [currentVideo, player])

  // Update refs when functions change
  useEffect(() => {
    startTimeMonitoringRef.current = startTimeMonitoring
  }, [startTimeMonitoring])

  useEffect(() => {
    handleTimeframeEndedRef.current = handleTimeframeEnded
  }, [handleTimeframeEnded])

  // Handle video ended - move to next video or handle looping
  const handleVideoEnded = useCallback((playerInstance?: any) => {
    const now = Date.now()

    // Prevent infinite loops by debouncing rapid video end events
    if (now - lastVideoEndTimeRef.current < 1000) {
      console.log('🔄 Video end event debounced (too soon after last event)')
      return
    }
    lastVideoEndTimeRef.current = now

    console.log('🔄 Video ended naturally')

    // Stop time monitoring
    if (timeMonitorRef.current) {
      clearInterval(timeMonitorRef.current)
      timeMonitorRef.current = null
    }

    // Use the video ID that was actually loaded in the player, not the current queue video
    const actualVideoId = lastLoadedVideoId.current
    if (!actualVideoId || !loopingManagerRef.current) {
      console.log('❌ No loaded video ID or looping manager when handling video end')
      return
    }

    // Find the video data for the actual video that ended
    const endedVideo = items.find(item => item.id === actualVideoId)
    if (!endedVideo) {
      console.log(`❌ Could not find video data for ended video: ${actualVideoId}`)
      return
    }

    console.log(`🔍 [DEBUG] Video ending: ${actualVideoId} (actual), currentVideo: ${currentVideo?.id}`)

    // Debug: Check if state exists before calling handleVideoEnd
    const existingState = loopingManagerRef.current.getState(actualVideoId)
    console.log(`🔍 [DEBUG] State exists for ${actualVideoId}: ${!!existingState}`)
    if (!existingState) {
      console.error(`❌ [DEBUG] State missing for video ${actualVideoId}. Available states:`,
        Array.from((loopingManagerRef.current as any).loopingStates.keys()))
    }

    const timeframes: TimeframeData[] = endedVideo.timeframes.map(tf => ({
      id: tf.id,
      startTime: tf.startTime,
      endTime: tf.endTime,
      loopCount: tf.loopCount
    }))

    // Use rule engine to handle video end
    const result = loopingManagerRef.current.handleVideoEnd(
      actualVideoId,
      timeframes,
      endedVideo.loopSettings
    )

    if (result === 'continue') {
      // Video handling continues (timeframes started or video looped)
      startTimeMonitoring(playerInstance || player)
      return
    }

    // Video is complete - queue progression will be handled by onVideoComplete callback
    console.log(`📊 Video complete - queue progression handled by looping manager`)
  }, [items, currentVideo, player, startTimeMonitoring])

  // Create player when API is ready and we have a video
  useEffect(() => {
    if (isPlayerReady && currentVideo && playerRef.current && !player) {
      console.log('🎬 [CREATE PLAYER] Creating YouTube player for:', currentVideo.title, 'ID:', currentVideo.id)

      // Mark this video as already loaded to prevent duplicate loading in VIDEO CHANGE EFFECT
      lastLoadedVideoId.current = currentVideo.id
      // Reset loading flag since we're creating a new player
      isLoadingVideo.current = false
      // Reset video end debounce timer for new video
      lastVideoEndTimeRef.current = 0

      // Use rule engine to determine start time
      const timeframes: TimeframeData[] = currentVideo.timeframes.map(tf => ({
        id: tf.id,
        startTime: tf.startTime,
        endTime: tf.endTime,
        loopCount: tf.loopCount
      }))

      const startTime = loopingManagerRef.current?.getVideoStartPosition(
        currentVideo.id,
        timeframes,
        currentVideo.loopSettings
      ) || 0

      // Initialize video in looping manager
      console.log(`🔍 [DEBUG] Initializing video in looping manager: ${currentVideo.id}`)
      loopingManagerRef.current?.initializeVideo(
        currentVideo.id,
        timeframes,
        currentVideo.loopSettings,
        queueLoopCount
      )
      console.log(`🔍 [DEBUG] Video initialized successfully: ${currentVideo.id}`)

      const newPlayer = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: currentVideo.id,
        playerVars: {
          autoplay: 1,
          controls: 1,
          rel: 0,
          modestbranding: 1,
          fs: 1,
          cc_load_policy: 0,
          iv_load_policy: 3,
          autohide: 0,
          start: Math.floor(startTime)
        },
        events: {
          onReady: (event: any) => {
            console.log('🎬 [onReady] YouTube player ready for video:', currentVideo?.id)
            setIsPlayerInstanceReady(true)
            // Clear loading flag when player is ready
            isLoadingVideo.current = false

            // Only auto-play if the queue is currently in playing state
            // This prevents auto-play when loading queues from URLs
            if (isPlaying) {
              console.log('🎬 [onReady] Starting playback for video:', currentVideo?.id)
              event.target.playVideo()
            } else {
              console.log('🎬 [onReady] Player ready but not auto-playing (isPlaying: false)')
            }

            if (currentVideo.timeframes.length > 0 && currentVideo.loopSettings.loopMode === 'timeframes-only') {
              console.log('🎬 [onReady] Video starting at first timeframe for timeframes-only mode')
            } else {
              console.log('🎬 [onReady] Video starting at beginning for whole-video mode')
            }

            // Time monitoring will be started when video starts playing (state 1)
          },
          onStateChange: (event: any) => {
            const state = event.data
            console.log('🎵 [onStateChange] Player state changed:', state, 'for video:', currentVideo?.id)

            // YouTube player states:
            // -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)

            if (state === 1) { // Playing
              setPlaying(true)

              // Start timeframe monitoring if needed using rule engine
              if (currentVideo && loopingManagerRef.current) {
                const timeframes: TimeframeData[] = currentVideo.timeframes.map(tf => ({
                  id: tf.id,
                  startTime: tf.startTime,
                  endTime: tf.endTime,
                  loopCount: tf.loopCount
                }))

                const shouldMonitor = loopingManagerRef.current.shouldStartTimeframeMonitoring(
                  currentVideo.id,
                  timeframes,
                  currentVideo.loopSettings
                )

                if (shouldMonitor) {
                  console.log(`⏰ [onStateChange] Starting timeframe monitoring`)
                  startTimeMonitoring(event.target)
                } else {
                  console.log(`⏰ [onStateChange] No timeframe monitoring needed yet`)
                }
              }
            } else if (state === 2) { // Paused
              setPlaying(false)
            } else if (state === 0) { // Ended
              setPlaying(false)
              handleVideoEnded()
            }
          },
          onError: (event: any) => {
            console.error('❌ YouTube player error:', event.data)
            // Try to skip to next video on error
            handleVideoEnded()
          }
        }
      })

      setPlayer(newPlayer)
      window.youtubePlayer = newPlayer
    }
  }, [isPlayerReady, currentVideo, player, handleVideoEnded, isPlaying, queueLoopCount, setPlaying, startTimeMonitoring])

  // Reset player instance ready state when player changes
  useEffect(() => {
    if (!player) {
      setIsPlayerInstanceReady(false)
    }
  }, [player])








  // Note: Removed aggressive state clearing that was interfering with video loop counters
  // Each video initializes its own state properly, so we don't need to clear states
  // when moving between videos in the same queue



  // Update player when current video changes (but not on initial load)
  useEffect(() => {
    console.log('🔄 [VIDEO CHANGE EFFECT] Triggered - player:', !!player, 'ready:', isPlayerInstanceReady, 'currentVideo:', currentVideo?.id, 'isLoading:', isLoadingVideo.current)

    // Skip if this is the initial video load (player was just created with this video)
    // or if we're already loading a video
    if (player && isPlayerInstanceReady && lastLoadedVideoId.current !== currentVideo?.id && !isLoadingVideo.current) {
      if (currentVideo && currentVideo.id) {
        console.log('🔄 [VIDEO CHANGE EFFECT] Loading new video:', currentVideo.title, 'ID:', currentVideo.id)

        // Set loading flag to prevent duplicate loads
        isLoadingVideo.current = true

        // Initialize video in looping manager
        const timeframes: TimeframeData[] = currentVideo.timeframes.map(tf => ({
          id: tf.id,
          startTime: tf.startTime,
          endTime: tf.endTime,
          loopCount: tf.loopCount
        }))

        console.log(`🔍 [DEBUG] Video change effect - Initializing video: ${currentVideo.id}`)
        loopingManagerRef.current?.initializeVideo(
          currentVideo.id,
          timeframes,
          currentVideo.loopSettings,
          queueLoopCount
        )
        console.log(`🔍 [DEBUG] Video change effect - Video initialized: ${currentVideo.id}`)

        try {
          console.log(`🔄 [VIDEO CHANGE EFFECT] Loading video ${currentVideo.id} from beginning`)

          // Track which video we're loading
          lastLoadedVideoId.current = currentVideo.id

          // Reset video end debounce timer for new video
          lastVideoEndTimeRef.current = 0

          // Use rule engine to determine start position
          const startTime = loopingManagerRef.current?.getVideoStartPosition(
            currentVideo.id,
            timeframes,
            currentVideo.loopSettings
          ) || 0

          console.log(`🔄 [VIDEO CHANGE EFFECT] Loading video ${currentVideo.id} starting at ${startTime}s`)
          player.loadVideoById({
            videoId: currentVideo.id,
            startSeconds: startTime
          })

          // Clear loading flag immediately since loadVideoById is synchronous
          isLoadingVideo.current = false
        } catch (error) {
          console.error('❌ Error loading video:', error)
          isLoadingVideo.current = false
        }
      } else {
        // No current video - destroy player
        console.log('🛑 No current video, destroying player')
        try {
          player.destroy()
          setPlayer(null)
          setIsPlayerInstanceReady(false)
          window.youtubePlayer = null
          if (timeMonitorRef.current) {
            clearInterval(timeMonitorRef.current)
            timeMonitorRef.current = null
          }
        } catch (error) {
          console.error('❌ Error destroying player:', error)
        }
      }
    }
  }, [player, isPlayerInstanceReady, currentVideo, queueLoopCount])

  // Control player based on isPlaying state
  useEffect(() => {
    // Don't try to control player if there's no current video (queue cleared)
    if (!currentVideo || !player || !isPlayerInstanceReady) {
      return
    }

    // Additional safety check to ensure player methods exist
    if (typeof player.getPlayerState !== 'function' ||
        typeof player.playVideo !== 'function' ||
        typeof player.pauseVideo !== 'function') {
      return
    }

    try {
      const playerState = player.getPlayerState()

      if (isPlaying && playerState !== 1) { // Not playing
        player.playVideo()
      } else if (!isPlaying && playerState === 1) { // Currently playing
        player.pauseVideo()
      }
    } catch (error) {
      console.error('❌ Error controlling player:', error)
    }
  }, [player, isPlayerInstanceReady, isPlaying, currentVideo])

  // Cleanup time monitoring on unmount
  useEffect(() => {
    return () => {
      if (timeMonitorRef.current) {
        clearInterval(timeMonitorRef.current)
        timeMonitorRef.current = null
      }
    }
  }, [])

  const contextValue = {
    currentTimeframeIndex,
    currentTimeframeLoopCount,
    currentTime,
    isInTimeframeMode,
  }

  return (
    <VideoPlayerContext.Provider value={contextValue}>
      <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="aspect-video bg-black relative">
        {currentVideo ? (
          <div
            ref={playerRef}
            className="w-full h-full"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-dark-400">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <p className="text-lg font-medium">{t('messages.noVideoSelected')}</p>
              <p className="text-sm">{t('messages.addVideosToQueue')}</p>
            </div>
          </div>
        )}
      </div>

      {currentVideo && (
        <div className="p-4 border-t border-white/10">
          <h3 className="font-medium text-white truncate mb-1">
            {currentVideo.title}
          </h3>
          <p className="text-sm text-dark-300 truncate mb-2">
            {currentVideo.channel || t('messages.unknownChannel')}
          </p>

          {/* Loop and timeframe information */}
          {(currentVideo.timeframes.length > 0 || currentVideo.loopSettings.videoLoopCount > 1) && (
            <div className="flex flex-wrap gap-2 text-xs">
              {/* Timeframes info */}
              {currentVideo.timeframes.length > 0 && (
                <div className="bg-primary-600/20 text-primary-300 px-2 py-1 rounded-md flex items-center gap-1">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <span>
                    TF {currentTimeframeIndex + 1}/{currentVideo.timeframes.length}
                    {currentVideo.loopSettings.loopMode === 'timeframes-only' ? ' Only' : ' + Full'}
                  </span>
                </div>
              )}

              {/* Current timeframe loop info */}
              {currentVideo.timeframes.length > 0 && (() => {
                const currentTimeframe = currentVideo.timeframes[currentTimeframeIndex]
                if (currentTimeframe && currentTimeframe.loopCount > 1) {
                  return (
                    <div className="bg-blue-600/20 text-blue-300 px-2 py-1 rounded-md flex items-center gap-1">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                      </svg>
                      <span>TF Loop {currentTimeframeLoopCount + 1}/{currentTimeframe.loopCount}</span>
                    </div>
                  )
                }
                return null
              })()}

              {/* Video loop count info */}
              {currentVideo.loopSettings.videoLoopCount > 1 && (
                <div className="bg-accent-600/20 text-accent-300 px-2 py-1 rounded-md flex items-center gap-1">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                  </svg>
                  <span>{(() => {
                    const state = loopingManagerRef.current?.getState(currentVideo.id)
                    return (state?.videoLoopCount || 0) + 1
                  })()}/{currentVideo.loopSettings.videoLoopCount}</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
      </div>
    </VideoPlayerContext.Provider>
  )
}

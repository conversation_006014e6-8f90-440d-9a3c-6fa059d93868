'use client'

import { QuickPlayUrlInput } from './QuickPlayUrlInput'
import { StandaloneVideoPlayer } from './StandaloneVideoPlayer'
import { QuickPlayControls } from './QuickPlayControls'
import { useQuickPlay } from '@/hooks/useQuickPlay'
import { useI18n } from '@/hooks/useI18n'

export function QuickPlayView() {
  const { currentVideo } = useQuickPlay()
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      {/* URL Input Section */}
      <div className="glassmorphism rounded-2xl p-6">
        <QuickPlayUrlInput />
      </div>

      {/* Video Player Section - Only show when video is loaded */}
      {currentVideo && (
        <>
          <div className="grid grid-cols-1 gap-6">
            {/* Video Player */}
            <StandaloneVideoPlayer />
            
            {/* Playback Controls */}
            <QuickPlayControls />
          </div>

          {/* Info Section */}
          <div className="glassmorphism rounded-2xl p-4">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-primary-600/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-primary-400">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-white mb-1">
                  {t('quickPlay.infoTitle')}
                </h3>
                <ul className="text-xs text-dark-300 space-y-1">
                  <li>• {t('quickPlay.infoPoint1')}</li>
                  <li>• {t('quickPlay.infoPoint2')}</li>
                  <li>• {t('quickPlay.infoPoint3')}</li>
                </ul>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Empty State Features */}
      {!currentVideo && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-primary-600/20 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-primary-400">
                <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
              </svg>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-white mb-2">
                {t('quickPlay.featuresTitle')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-dark-300">
                <div className="flex items-center space-x-2">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-green-400 flex-shrink-0">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  <span>{t('quickPlay.feature1')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-green-400 flex-shrink-0">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  <span>{t('quickPlay.feature2')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-green-400 flex-shrink-0">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  <span>{t('quickPlay.feature3')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-green-400 flex-shrink-0">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                  <span>{t('quickPlay.feature4')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

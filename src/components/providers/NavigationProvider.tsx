'use client'

import { ReactNode, useState } from 'react'
import { NavigationContext, NavigationView, TimeframeEditingState } from '@/hooks/useNavigation'

interface NavigationProviderProps {
  children: ReactNode
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const [activeView, setActiveView] = useState<NavigationView>('quick-play')
  const [timeframeEditingState, setTimeframeEditingState] = useState<TimeframeEditingState | null>(null)

  const enterTimeframeEditing = (state: TimeframeEditingState) => {
    setTimeframeEditingState(state)
    setActiveView('timeframe-edit')
  }

  const exitTimeframeEditing = () => {
    const returnView = timeframeEditingState?.returnView || 'quick-play'
    setTimeframeEditingState(null)
    setActiveView(returnView)
  }

  return (
    <NavigationContext.Provider value={{
      activeView,
      setActiveView,
      timeframeEditingState,
      enterTimeframeEditing,
      exitTimeframeEditing
    }}>
      {children}
    </NavigationContext.Provider>
  )
}

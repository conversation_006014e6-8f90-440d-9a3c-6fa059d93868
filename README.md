# YouTube Looper - Next.js Migration

A modern, feature-rich YouTube video queue and looper application built with Next.js, TypeScript, and Tailwind CSS. This is a complete migration from the original Hugo-based YouTube Looper with enhanced functionality and improved architecture.

## 🚀 Features

### Core Functionality
- **Video Search**: Real-time YouTube video search using YouTube Data API v3
- **Queue Management**: Add, remove, reorder videos with drag-and-drop support
- **Video Player**: Embedded YouTube player with custom controls
- **Infinite Looping**: Automatically loops through your entire queue
- **Persistent Storage**: Cloud-based queue storage with Firebase Firestore

### Advanced Features
- **Personal Queues**: Create and manage private video queues
- **Public Queue Sharing**: Share queues publicly and discover community queues
- **Real-time Sync**: Instant synchronization across devices
- **Authentication**: Google OAuth and email/password authentication
- **Responsive Design**: Mobile-first design with glassmorphism UI
- **Enhanced UX**: Scrolling text animations, loading states, and smooth transitions

### Technical Features
- **TypeScript**: Full type safety and better developer experience
- **Modern React**: Hooks, Context API, and functional components
- **Tailwind CSS**: Utility-first CSS framework with custom design system
- **Firebase Integration**: Authentication, Firestore, and real-time updates
- **YouTube API**: Official YouTube Data API v3 and IFrame API integration
- **Performance Optimized**: Code splitting, lazy loading, and optimized images

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.4 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth (Google OAuth, Email/Password, Anonymous)
- **APIs**: YouTube Data API v3, YouTube IFrame API
- **State Management**: React Context + useReducer
- **Image Optimization**: Next.js Image component
- **Deployment**: Vercel (recommended) or any Node.js hosting

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd youtube-looper-nextjs
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Fill in your API keys and Firebase configuration:
   ```env
   # YouTube API Configuration
   NEXT_PUBLIC_YOUTUBE_API_KEY=your_youtube_api_key_here

   # Firebase Configuration
   NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Configuration

### YouTube API Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable YouTube Data API v3
4. Create credentials (API Key)
5. Add the API key to your `.env.local` file

### Firebase Setup
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. **Enable Authentication**:
   - Go to Authentication → Sign-in method
   - Enable Google, Email/Password, and Anonymous providers
4. **Create Firestore Database**:
   - Go to Firestore Database → Create database
   - Start in test mode (rules will be deployed later)
5. **Add Web App**:
   - Go to Project Settings → General → Your apps
   - Click "Add app" → Web
   - Register your app and copy the config
6. **Update Firebase project ID**:
   - Edit `.firebaserc` and replace `your-firebase-project-id` with your actual project ID
7. **Deploy Firestore rules**:
   ```bash
   npm run firebase:deploy:firestore
   ```
8. Copy configuration to your `.env.local` file

### Password Protection (Optional)

To protect your deployed app with a password stored securely in Firebase:

1. **Set up the password in Firebase Console**:
   - Go to your Firebase Console → Firestore Database
   - Create a new collection called `app_config`
   - Create a document with ID `access_control`
   - Add a field: `password` (string) with your desired password value

2. **Alternative: Use the setup script**:
   ```bash
   # Development environment (default)
   npm run setup:password "your_dev_password"

   # Production environment
   node scripts/setup-password.js "your_prod_password" --env=production

   # Interactive setup (will prompt for password)
   npm run setup:password                              # Development
   node scripts/setup-password.js --env=production     # Production
   ```

3. **Alternative: Use Firebase CLI** (if you have it set up):
   ```bash
   # Create the password document using Firebase CLI
   firebase firestore:set app_config/access_control '{"password": "your_secret_password_here"}'
   ```

4. **How it works**:
   - Password is stored securely in Firestore (server-side only)
   - Client sends password to API route for verification
   - Password verification is cached locally for 24 hours
   - If no password is configured in Firebase, the app allows free access
   - Perfect for private deployments with proper security

5. **Security benefits**:
   - Password is never exposed in client-side code
   - Server-side verification through Next.js API routes
   - Firestore rules prevent client access to password data
   - Suitable for production deployments requiring access control

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── auth/             # Authentication components
│   ├── layout/           # Layout components
│   ├── providers/        # Context providers
│   ├── queue/            # Queue management components
│   ├── search/           # Search functionality
│   ├── video-player/     # Video player components
│   ├── personal-queues/  # Personal queue management
│   └── public-queues/    # Public queue browsing
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
│   ├── firebase/         # Firebase configuration
│   ├── types/            # TypeScript type definitions
│   └── utils/            # Utility functions
└── styles/               # Additional styles
```

## 🎯 Migration Status

### ✅ Completed
- [x] Project setup and configuration
- [x] Core layout and navigation
- [x] TypeScript type definitions
- [x] Firebase configuration
- [x] Authentication system structure
- [x] Queue management system
- [x] Video player component
- [x] Search functionality structure
- [x] Responsive design foundation
- [x] Glassmorphism UI design

### 🚧 In Progress
- [ ] YouTube API integration
- [ ] Firebase authentication implementation
- [ ] Video player YouTube API integration
- [ ] Search results implementation
- [ ] Personal queue management
- [ ] Public queue sharing
- [ ] Real-time synchronization

### 📋 Todo
- [ ] Drag and drop queue reordering
- [ ] Advanced search filters
- [ ] Queue export/import
- [ ] Keyboard shortcuts
- [ ] Accessibility improvements
- [ ] Performance optimizations
- [ ] Unit and integration tests
- [ ] Documentation completion

## 🚀 Deployment

### Firebase Hosting (Recommended)
1. **Setup Firebase CLI**:
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

2. **Configure project**:
   ```bash
   firebase use --add
   # Select your Firebase project
   ```

3. **Deploy**:
   ```bash
   npm run firebase:deploy
   ```

### Vercel
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
The application can be deployed to any platform that supports static hosting:
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
- DigitalOcean App Platform

### Firebase Emulators (Development)
Run Firebase emulators locally for development:
```bash
npm run firebase:emulators
```
This starts:
- Firestore emulator on port 8080
- Auth emulator on port 9099
- Firebase UI on port 4000

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

## 🔗 Links

- [Original YouTube Looper](../youtube-looper/)
- [Tubli Website](https://tubli.to)

## 📞 Support

For questions or support, please create an issue in this repository.

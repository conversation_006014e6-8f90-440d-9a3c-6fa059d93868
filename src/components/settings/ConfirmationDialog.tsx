'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { useI18n } from '@/hooks/useI18n'

interface ConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  isLoading: boolean
  userEmail: string
  queueCount?: number
  videoCount?: number
}

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  userEmail,
  queueCount = 0,
  videoCount = 0
}: ConfirmationDialogProps) {
  const [confirmationText, setConfirmationText] = useState('')
  const [isMounted, setIsMounted] = useState(false)
  const { t } = useI18n()
  
  const requiredText = 'DELETE MY ACCOUNT'
  const isConfirmationValid = confirmationText === requiredText

  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (isOpen) {
      setConfirmationText('')
    }
  }, [isOpen])

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !isLoading) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, isLoading, onClose])

  if (!isMounted || !isOpen) return null

  const handleConfirm = () => {
    if (isConfirmationValid && !isLoading) {
      onConfirm()
    }
  }

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={!isLoading ? onClose : undefined}
      />
      
      {/* Dialog */}
      <div role="dialog" className="relative w-full max-w-md bg-dark-800 rounded-2xl border border-red-500/30 shadow-2xl">
        {/* Header */}
        <div className="p-6 border-b border-red-500/20">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-red-400">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-bold text-red-400">{t('buttons.deleteAccount')}</h2>
              <p className="text-sm text-red-200">{t('errors.accountDeletionWarning')}</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Warning */}
          <div className="bg-red-900/30 rounded-lg p-4 border border-red-500/20">
            <p className="text-red-200 text-sm leading-relaxed">
              <strong>{t('common.warning')}:</strong> {t('errors.deletionWarning')}
              <span className="font-mono text-red-300"> {userEmail}</span>.
              {t('errors.thisWillRemove', {
                queueCount,
                queuePlural: queueCount !== 1 ? 's' : '',
                videoText: videoCount > 0 ? t('errors.withVideos', {
                  videoCount,
                  videoPlural: videoCount !== 1 ? 's' : ''
                }) : ''
              })}
            </p>
          </div>

          {/* Confirmation Input */}
          <div>
            <label htmlFor="confirmation" className="block text-sm font-medium text-red-200 mb-3">
              {t('labels.confirmDeletion')} <span className="font-mono bg-red-900/50 px-2 py-1 rounded text-red-300">{requiredText}</span> {t('labels.below')}:
            </label>
            <input
              type="text"
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              disabled={isLoading}
              className="w-full px-4 py-3 bg-dark-700/50 border border-red-500/30 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              placeholder={requiredText}
              autoComplete="off"
            />
          </div>

          {/* Data Summary */}
          <div className="bg-dark-700/30 rounded-lg p-4">
            <h4 className="font-semibold text-dark-200 mb-2">{t('labels.dataToBeDeleted')}:</h4>
            <ul className="text-sm text-dark-300 space-y-1">
              <li>• {t('accountDeletion.profileInformation')}</li>
              <li>• {t('accountDeletion.privateQueues')}</li>
              <li>• {t('accountDeletion.publicQueuesData')}</li>
              <li>• {t('accountDeletion.associatedData')}</li>
              <li>• {t('accountDeletion.authenticationData')}</li>
            </ul>
            <div className="mt-3 p-3 bg-red-900/20 rounded border border-red-500/20">
              <p className="text-xs text-red-300">
                <strong>{t('common.note')}:</strong> {t('accountDeletion.complianceNote')}
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-6 border-t border-dark-700/50 flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 bg-dark-600 hover:bg-dark-500 disabled:bg-dark-700 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-all"
          >
            {t('common.cancel')}
          </button>
          <button
            onClick={handleConfirm}
            disabled={!isConfirmationValid || isLoading}
            className="px-6 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-all flex items-center space-x-2"
          >
            {isLoading && (
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            )}
            <span>{isLoading ? t('status.deleting') : t('buttons.deleteAccount')}</span>
          </button>
        </div>
      </div>
    </div>,
    document.body
  )
}

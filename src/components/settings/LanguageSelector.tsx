'use client'

import { useState } from 'react'
import { useI18n } from '@/hooks/useI18n'
import { useToast } from '@/components/providers/ToastProvider'
import { SupportedLanguage } from '@/lib/i18n/types'

export function LanguageSelector() {
  const { language, setLanguage, availableLanguages, isLoading } = useI18n()
  const { t } = useI18n()
  const { showToast } = useToast()
  const [isChanging, setIsChanging] = useState(false)

  const handleLanguageChange = async (newLanguage: SupportedLanguage) => {
    if (newLanguage === language || isChanging) return

    setIsChanging(true)
    try {
      await setLanguage(newLanguage)
      showToast(t('settings.languageChanged'), 'success')
    } catch (error) {
      console.error('Failed to change language:', error)
      showToast(t('errors.generic'), 'error')
    } finally {
      setIsChanging(false)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-dark-800/50 rounded-2xl p-6 border border-dark-700/50">
        <h2 className="text-xl font-bold text-white mb-6">{t('settings.languageSettings')}</h2>
        <div className="flex items-center justify-center py-8">
          <div className="w-6 h-6 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-dark-800/50 rounded-2xl p-6 border border-dark-700/50">
      <h2 className="text-xl font-bold text-white mb-6">{t('settings.languageSettings')}</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-dark-200 mb-3">
            {t('settings.selectLanguage')}
          </label>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {availableLanguages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                disabled={isChanging || language === lang.code}
                className={`
                  relative p-4 rounded-lg border transition-all duration-200 text-left
                  ${language === lang.code
                    ? 'bg-primary-600/20 border-primary-500 text-white'
                    : 'bg-dark-700/50 border-dark-600/50 text-dark-200 hover:bg-dark-700 hover:border-dark-500'
                  }
                  ${isChanging ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
              >
                {/* Selected indicator */}
                {language === lang.code && (
                  <div className="absolute top-2 right-2">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-primary-400">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                  </div>
                )}
                
                {/* Language info */}
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{lang.flag}</span>
                  <div>
                    <div className="font-medium">{lang.name}</div>
                    <div className="text-sm opacity-75">{lang.nativeName}</div>
                  </div>
                </div>
                
                {/* Loading indicator for changing language */}
                {isChanging && language === lang.code && (
                  <div className="absolute inset-0 flex items-center justify-center bg-dark-800/50 rounded-lg">
                    <div className="w-4 h-4 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
        
        {/* Language info */}
        <div className="text-sm text-dark-400 bg-dark-700/30 rounded-lg p-3">
          <p className="mb-1">
            <strong className="text-dark-300">Current:</strong> {availableLanguages.find(l => l.code === language)?.name}
          </p>
          <p>
            Language changes take effect immediately and are saved to your browser.
          </p>
        </div>
      </div>
    </div>
  )
}

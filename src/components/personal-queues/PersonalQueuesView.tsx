'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useQueue } from '@/hooks/useQueue'
import { useI18n } from '@/hooks/useI18n'
import { firebaseService } from '@/lib/services/firebase'
import { Queue } from '@/lib/types/queue'
import { PersonalQueueCard } from './PersonalQueueCard'
import { QueueBulkActions } from './QueueBulkActions'
import { QueueStats } from './QueueStats'

export function PersonalQueuesView() {
  const { user, isAuthenticated } = useAuth()
  const { loadQueue } = useQueue()
  const [personalQueues, setPersonalQueues] = useState<Queue[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { t } = useI18n()
  const [selectedQueues, setSelectedQueues] = useState<Queue[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Use real-time listener for better offline support
  useEffect(() => {
    if (!isAuthenticated || !user) {
      setIsLoading(false)
      setPersonalQueues([])
      return
    }

    setIsLoading(true)
    console.log('🔄 Setting up real-time listener for personal queues')

    // Subscribe to real-time updates with offline support
    const unsubscribe = firebaseService.subscribeToPersonalQueuesWithMetadata(user.uid, (queues, metadata) => {
      console.log(`📋 Received ${queues.length} personal queues from ${metadata.fromCache ? 'cache' : 'server'}`)
      setPersonalQueues(queues)
      setIsLoading(false)
    })

    return () => {
      console.log('🔌 Unsubscribing from personal queues listener')
      unsubscribe()
    }
  }, [isAuthenticated, user])

  // Fallback method for manual refresh (still useful for error recovery)
  const loadPersonalQueues = useCallback(async () => {
    if (!user) return

    try {
      setIsLoading(true)
      const queues = await firebaseService.getUserQueues(user.uid)
      setPersonalQueues(queues)
    } catch (error) {
      console.error('Failed to load personal queues:', error)
    } finally {
      setIsLoading(false)
    }
  }, [user])



  const handleLoadQueue = (queue: Queue) => {
    loadQueue(queue.queueData)
    console.log('✅ Queue loaded:', queue.metadata.title)
  }

  const handleDeleteQueue = async (queueId: string) => {
    try {
      const success = await firebaseService.deleteQueue(queueId, user!.uid)
      if (success) {
        await loadPersonalQueues() // Refresh the list
      }
    } catch (error) {
      console.error('Failed to delete queue:', error)
    }
  }



  const handleQueueUpdate = (updatedQueue: Queue) => {
    // Update the queue in the local state
    setPersonalQueues(queues =>
      queues.map(queue =>
        queue.id === updatedQueue.id ? updatedQueue : queue
      )
    )
  }

  const handleBulkActionComplete = async () => {
    // Refresh the queue list after bulk actions
    await loadPersonalQueues()
    setSelectedQueues([])
  }

  const toggleBulkActions = () => {
    setShowBulkActions(!showBulkActions)
    if (showBulkActions) {
      setSelectedQueues([])
    }
  }

  const handleQueueSelectionChange = (queue: Queue, selected: boolean) => {
    if (selected) {
      setSelectedQueues(prev => [...prev, queue])
    } else {
      setSelectedQueues(prev => prev.filter(q => q.id !== queue.id))
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <div className="glassmorphism rounded-2xl p-6">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 6h-3V5h-2v3H8v2h3v3h2v-3h3V8z"/>
              </svg>
            </div>
            <p className="text-dark-300 mb-2">{t('messages.signInRequired')}</p>
            <p className="text-sm text-dark-400">{t('messages.signInToManageQueues')}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">
              {t('personalQueues.myQueues')}
            </h1>
            <p className="text-dark-300">
              {t('personalQueues.description')}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {/* Refresh button */}
            <button
              onClick={loadPersonalQueues}
              disabled={isLoading}
              className="p-2 rounded-lg bg-dark-700 text-dark-300 hover:bg-dark-600 hover:text-white transition-all duration-200 disabled:opacity-50"
              title={t('buttons.refreshQueues')}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className={isLoading ? 'animate-spin' : ''}>
                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </button>

            {personalQueues.length > 0 && (
              <button
                onClick={toggleBulkActions}
                className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  showBulkActions
                    ? 'bg-primary-600 text-white'
                    : 'bg-dark-700 text-dark-300 hover:bg-dark-600 hover:text-white'
                }`}
              >
                {showBulkActions ? t('buttons.cancelSelection') : t('buttons.bulkActions')}
              </button>
            )}
            <div className="text-right">
              <p className="text-sm text-white font-medium">{personalQueues.length}</p>
              <p className="text-xs text-dark-400">{t('labels.totalQueuesCount')}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-white font-medium">
                {personalQueues.filter(q => q.isPublic).length}
              </p>
              <p className="text-xs text-dark-400">{t('labels.publicCount')}</p>
            </div>
          </div>
        </div>
      </div>



      {/* Bulk Actions */}
      {showBulkActions && personalQueues.length > 0 && (
        <QueueBulkActions
          selectedQueues={selectedQueues}
          onSelectionChange={setSelectedQueues}
          onBulkActionComplete={handleBulkActionComplete}
          allQueues={personalQueues}
        />
      )}

      {/* Queues List */}
      <div className="glassmorphism rounded-2xl overflow-hidden">
        {isLoading ? (
          <div className="p-6 text-center">
            <div className="loading-spinner w-8 h-8 mx-auto mb-3"></div>
            <p className="text-dark-300">{t('status.loadingQueues')}</p>
          </div>
        ) : personalQueues.length === 0 ? (
          <div className="p-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
                <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
              </svg>
            </div>
            <p className="text-dark-300 mb-2">{t('messages.noQueuesYet')}</p>
            <p className="text-sm text-dark-400">{t('messages.createFirstQueue')}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
            {personalQueues.map((queue) => (
              <PersonalQueueCard
                key={queue.id}
                queue={queue}
                onLoad={handleLoadQueue}
                onDelete={handleDeleteQueue}
                onPrivacyUpdate={loadPersonalQueues}
                onUpdate={handleQueueUpdate}
                isSelectionMode={showBulkActions}
                isSelected={selectedQueues.some(q => q.id === queue.id)}
                onSelectionChange={handleQueueSelectionChange}
              />
            ))}
          </div>
        )}
      </div>

      {/* Queue Statistics */}
      {personalQueues.length > 0 && !showBulkActions && (
        <QueueStats queues={personalQueues} />
      )}
    </div>
  )
}

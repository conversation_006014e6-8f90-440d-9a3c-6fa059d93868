'use client'

import { useDebouncedTimeframe } from '@/hooks/useDebouncedTimeframe'
import { RangeSlider } from '@/components/ui/RangeSlider'
import { VideoTimeframe } from '@/lib/types/video'

interface TimeframeEditorProps {
  timeframe: VideoTimeframe
  maxDuration: number
  onUpdate: (timeframeId: string, updates: Partial<Pick<VideoTimeframe, 'startTime' | 'endTime' | 'loopCount'>>) => void
  onClose: () => void
}

export function TimeframeEditor({ timeframe, maxDuration, onUpdate, onClose }: TimeframeEditorProps) {
  const { localStart, localEnd, isUpdating, updateTimeframe } = useDebouncedTimeframe({
    initialStart: timeframe.startTime,
    initialEnd: timeframe.endTime,
    onUpdate: (start, end) => {
      onUpdate(timeframe.id, { startTime: start, endTime: end })
    },
    debounceMs: 150
  })

  return (
    <div className="mt-4 p-5 bg-dark-700/60 rounded-xl border border-dark-600/50 shadow-lg">
      <div className="flex items-center justify-between mb-5">
        <label className="text-base text-dark-100 font-semibold">Edit Time Range</label>
        {isUpdating && (
          <div className="flex items-center gap-2 text-sm text-primary-400">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-400"></div>
            Updating...
          </div>
        )}
      </div>

      <RangeSlider
        min={0}
        max={maxDuration}
        startValue={localStart}
        endValue={localEnd}
        onChange={updateTimeframe}
        step={1}
        className="mb-5"
      />

      <div className="flex justify-end">
        <button
          onClick={onClose}
          className="text-sm px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-all hover:scale-105 active:scale-95 shadow-lg"
        >
          Done Editing
        </button>
      </div>
    </div>
  )
}

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { I18nProvider } from '@/components/providers/I18nProvider'
import { useI18n } from '@/hooks/useI18n'
import { DEFAULT_LANGUAGE } from '@/lib/i18n/types'
import { loadTranslations, getTranslation, interpolateString } from '@/lib/i18n/utils'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Test component that uses i18n
function TestComponent() {
  const { t, language, setLanguage, availableLanguages, isLoading } = useI18n()

  if (isLoading) {
    return <div data-testid="loading">Loading...</div>
  }

  return (
    <div>
      <div data-testid="current-language">{language}</div>
      <div data-testid="translated-text">{t('common.loading')}</div>
      <div data-testid="nested-translation">{t('settings.title')}</div>
      <div data-testid="interpolated-text">{t('time.minutesAgo', { count: 5 })}</div>
      <div data-testid="missing-key">{t('nonexistent.key')}</div>
      <div data-testid="available-languages">{availableLanguages.length}</div>
      <button
        data-testid="change-language"
        onClick={() => setLanguage('es')}
      >
        Change Language
      </button>
    </div>
  )
}

describe('I18n System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  describe('loadTranslations', () => {
    it('should load English translations', async () => {
      const translations = await loadTranslations('en')
      expect(translations).toBeDefined()
      expect(translations.common).toBeDefined()
      expect(translations.common.loading).toBe('Loading...')
    })

    it('should fallback to default language for invalid language', async () => {
      const translations = await loadTranslations('invalid' as any)
      expect(translations).toBeDefined()
      // Should fallback to English
      expect(translations.common?.loading).toBe('Loading...')
    })
  })

  describe('getTranslation', () => {
    const mockTranslations = {
      common: {
        loading: 'Loading...',
        save: 'Save'
      },
      nested: {
        deep: {
          value: 'Deep Value'
        }
      }
    }

    it('should get simple translation', () => {
      const result = getTranslation(mockTranslations, 'common.loading')
      expect(result).toBe('Loading...')
    })

    it('should get nested translation', () => {
      const result = getTranslation(mockTranslations, 'nested.deep.value')
      expect(result).toBe('Deep Value')
    })

    it('should return key for missing translation', () => {
      const result = getTranslation(mockTranslations, 'missing.key')
      expect(result).toBe('missing.key')
    })

    it('should use fallback translations', () => {
      const fallback = { missing: { key: 'Fallback Value' } }
      const result = getTranslation({}, 'missing.key', undefined, fallback)
      expect(result).toBe('Fallback Value')
    })
  })

  describe('interpolateString', () => {
    it('should interpolate single value', () => {
      const result = interpolateString('Hello {name}', { name: 'World' })
      expect(result).toBe('Hello World')
    })

    it('should interpolate multiple values', () => {
      const result = interpolateString('{count} items in {location}', { 
        count: 5, 
        location: 'basket' 
      })
      expect(result).toBe('5 items in basket')
    })

    it('should handle missing values', () => {
      const result = interpolateString('Hello {name}', {})
      expect(result).toBe('Hello {name}')
    })

    it('should handle no values', () => {
      const result = interpolateString('Hello World')
      expect(result).toBe('Hello World')
    })
  })

  describe('I18nProvider', () => {
    it('should render with default language', async () => {
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      expect(screen.getByTestId('current-language')).toHaveTextContent(DEFAULT_LANGUAGE)
      expect(screen.getByTestId('translated-text')).toHaveTextContent('Loading...')
      expect(screen.getByTestId('available-languages')).toHaveTextContent('3')
    })

    it('should handle missing translation keys', async () => {
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      // Should return the key itself for missing translations
      expect(screen.getByTestId('missing-key')).toHaveTextContent('nonexistent.key')
    })

    it('should handle interpolation', async () => {
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      expect(screen.getByTestId('interpolated-text')).toHaveTextContent('5 minutes ago')
    })

    it('should persist language selection', async () => {
      render(
        <I18nProvider>
          <TestComponent />
        </I18nProvider>
      )

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument()
      })

      fireEvent.click(screen.getByTestId('change-language'))

      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('youtube-looper-language', 'es')
      })
    })
  })
})

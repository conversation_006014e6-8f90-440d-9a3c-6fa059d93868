'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { useQuickPlay } from '@/hooks/useQuickPlay'
import { useI18n } from '@/hooks/useI18n'
import { YouTubePlayer } from '@/lib/types/video'

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
  }
}

export function StandaloneVideoPlayer() {
  const {
    currentVideo,
    isPlaying,
    isLooping,
    volume,
    setPlaying,
    setError
  } = useQuickPlay()
  
  const playerRef = useRef<HTMLDivElement>(null)
  const [player, setPlayer] = useState<YouTubePlayer | null>(null)
  const [isPlayerReady, setIsPlayerReady] = useState(false)
  const [isPlayerInstanceReady, setIsPlayerInstanceReady] = useState(false)
  const [loopAttempts, setLoopAttempts] = useState(0)
  const { t } = useI18n()

  const playerInstanceRef = useRef<YouTubePlayer | null>(null)
  const lastLoadedVideoId = useRef<string | null>(null)

  // Keep refs in sync
  useEffect(() => {
    playerInstanceRef.current = player
  }, [player])

  // Initialize YouTube API
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.YT) {
      console.log('📺 [Quick Play] Loading YouTube IFrame API...')

      // Set up the callback for when API is ready
      window.onYouTubeIframeAPIReady = () => {
        console.log('✅ [Quick Play] YouTube IFrame API ready')
        setIsPlayerReady(true)
      }

      // Load the API if not already loaded
      if (!document.querySelector('script[src*="youtube.com/iframe_api"]')) {
        const tag = document.createElement('script')
        tag.src = 'https://www.youtube.com/iframe_api'
        const firstScriptTag = document.getElementsByTagName('script')[0]
        if (firstScriptTag && firstScriptTag.parentNode) {
          firstScriptTag.parentNode.insertBefore(tag, firstScriptTag)
        } else {
          // Fallback for test environments
          document.head.appendChild(tag)
        }
      }
    } else if (window.YT) {
      setIsPlayerReady(true)
    }
  }, [])

  // Handle video ended - restart if looping
  const handleVideoEnded = useCallback(() => {
    console.log('🎬 [Quick Play] Video ended, looping:', isLooping, 'attempts:', loopAttempts)

    if (isLooping && playerInstanceRef.current && currentVideo) {
      // Use a small delay to ensure the player is ready for the restart
      setTimeout(() => {
        try {
          if (playerInstanceRef.current && currentVideo) {
            console.log('🔄 [Quick Play] Restarting video for loop:', currentVideo.title)

            // Try different approaches based on attempt count
            if (loopAttempts < 2) {
              // First attempts: try seek + play
              playerInstanceRef.current.seekTo(0, true)
              setTimeout(() => {
                if (playerInstanceRef.current) {
                  playerInstanceRef.current.playVideo()
                }
              }, 50)
            } else {
              // Fallback: reload the video entirely
              console.log('🔄 [Quick Play] Using fallback: reloading video')
              playerInstanceRef.current.loadVideoById(currentVideo.id)
            }

            setLoopAttempts(prev => prev + 1)
          }
        } catch (error) {
          console.error('❌ [Quick Play] Error restarting video:', error)
          setError('Failed to restart video')
        }
      }, 200) // Slightly longer delay to let the player settle
    } else {
      setPlaying(false)
      setLoopAttempts(0) // Reset attempts when not looping
    }
  }, [isLooping, currentVideo, loopAttempts, setPlaying, setError])

  // Create player when API is ready and we have a video
  useEffect(() => {
    if (isPlayerReady && currentVideo && playerRef.current && !player) {
      console.log('🎬 [Quick Play] Creating YouTube player for:', currentVideo.title, 'ID:', currentVideo.id)

      lastLoadedVideoId.current = currentVideo.id

      const newPlayer = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: currentVideo.id,
        playerVars: {
          autoplay: 1,
          controls: 1,
          rel: 0,
          modestbranding: 1,
          fs: 1,
          cc_load_policy: 0,
          iv_load_policy: 3,
          autohide: 0,
          // Note: YouTube's native loop parameter only works for playlists
          // We handle looping manually in the onStateChange event
        },
        events: {
          onReady: (event: any) => {
            console.log('✅ [Quick Play] Player ready')
            setIsPlayerInstanceReady(true)
            
            // Set initial volume
            try {
              event.target.setVolume(volume * 100)
            } catch (error) {
              console.warn('⚠️ [Quick Play] Could not set initial volume:', error)
            }
          },
          onStateChange: (event: any) => {
            const state = event.data
            console.log('🎬 [Quick Play] Player state changed:', state)

            // YouTube player states:
            // -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)

            if (state === 1) { // Playing
              setPlaying(true)
            } else if (state === 2) { // Paused
              setPlaying(false)
            } else if (state === 0) { // Ended
              console.log('🎬 [Quick Play] Video ended, handling...')
              setPlaying(false)
              handleVideoEnded()
            }
          },
          onError: (event: any) => {
            console.error('❌ [Quick Play] YouTube player error:', event.data)
            setError('Video playback error')
          }
        }
      })

      setPlayer(newPlayer)
    }
  }, [isPlayerReady, currentVideo, player, handleVideoEnded, volume, setPlaying, setError])

  // Handle video changes
  useEffect(() => {
    if (player && isPlayerInstanceReady && currentVideo) {
      if (lastLoadedVideoId.current !== currentVideo.id) {
        console.log('🎬 [Quick Play] Loading new video:', currentVideo.title, 'ID:', currentVideo.id)

        try {
          player.loadVideoById(currentVideo.id)
          lastLoadedVideoId.current = currentVideo.id
          setLoopAttempts(0) // Reset loop attempts for new video
        } catch (error) {
          console.error('❌ [Quick Play] Error loading video:', error)
          setError('Failed to load video')
        }
      }
    } else if (player && !currentVideo) {
      // No current video - destroy player
      console.log('🛑 [Quick Play] No current video, destroying player')
      try {
        player.destroy()
        setPlayer(null)
        setIsPlayerInstanceReady(false)
        lastLoadedVideoId.current = null
        setLoopAttempts(0) // Reset loop attempts
      } catch (error) {
        console.error('❌ [Quick Play] Error destroying player:', error)
      }
    }
  }, [player, isPlayerInstanceReady, currentVideo, setError])

  // Handle play/pause state changes
  useEffect(() => {
    if (player && isPlayerInstanceReady) {
      try {
        if (isPlaying) {
          player.playVideo()
        } else {
          player.pauseVideo()
        }
      } catch (error) {
        console.error('❌ [Quick Play] Error controlling playback:', error)
      }
    }
  }, [player, isPlayerInstanceReady, isPlaying])

  // Handle volume changes
  useEffect(() => {
    if (player && isPlayerInstanceReady) {
      try {
        player.setVolume(volume * 100)
      } catch (error) {
        console.error('❌ [Quick Play] Error setting volume:', error)
      }
    }
  }, [player, isPlayerInstanceReady, volume])

  return (
    <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="aspect-video bg-black relative">
        {currentVideo ? (
          <div
            ref={playerRef}
            className="w-full h-full"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-dark-400">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <p className="text-lg font-medium">{t('quickPlay.noVideoLoaded')}</p>
              <p className="text-sm">{t('quickPlay.enterYouTubeUrl')}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

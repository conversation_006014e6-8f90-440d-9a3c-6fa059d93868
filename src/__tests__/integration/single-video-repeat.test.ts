/**
 * Test suite for single-video repeat functionality
 * This tests the fix for the bug where repeat functionality didn't work
 * when there was only one video in the queue/playlist.
 */

import { VideoPlayerLoopingManager } from '@/lib/looping/VideoPlayerLoopingManager'
import { LoopingRuleEngine } from '@/lib/looping/LoopingRuleEngine'
import { VideoLoopSettings, TimeframeData } from '@/lib/types/video'

describe('Single Video Repeat Functionality', () => {
  let loopingManager: VideoPlayerLoopingManager
  let ruleEngine: LoopingRuleEngine
  let mockOnStateUpdate: jest.Mock
  let mockOnTimeframeStart: jest.Mock
  let mockOnVideoComplete: jest.Mock

  beforeEach(() => {
    // Reset mocks
    mockOnStateUpdate = jest.fn()
    mockOnTimeframeStart = jest.fn()
    mockOnVideoComplete = jest.fn()

    // Create fresh instances
    ruleEngine = new LoopingRuleEngine()
    loopingManager = new VideoPlayerLoopingManager(
      mockOnStateUpdate,
      mockOnTimeframeStart,
      mockOnVideoComplete
    )
  })

  describe('Single Video Without Timeframes', () => {
    test('Should loop single video multiple times before completing', () => {
      const videoId = 'single-video-test'
      const timeframes: TimeframeData[] = []
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 3, // Should loop 3 times
        loopMode: 'whole-video-plus-timeframes'
      }
      const queueLoopCount = -1 // Infinite queue loops

      // Initialize video
      loopingManager.initializeVideo(videoId, timeframes, loopSettings, queueLoopCount)
      let state = loopingManager.getState(videoId)!

      // Simulate video ending 3 times
      for (let i = 0; i < 3; i++) {
        console.log(`\n--- Video End Simulation ${i + 1}/3 ---`)
        
        const result = loopingManager.handleVideoEnd(videoId, timeframes, loopSettings)
        state = loopingManager.getState(videoId)!
        
        if (i < 2) {
          // First 2 times should continue (video loops)
          expect(result).toBe('continue')
          expect(state.videoLoopCount).toBe(i + 1)
          expect(mockOnVideoComplete).not.toHaveBeenCalled()
        } else {
          // 3rd time should complete
          expect(result).toBe('complete')
          expect(state.videoLoopCount).toBe(3) // State is updated after completion check
          expect(mockOnVideoComplete).toHaveBeenCalled()
        }
      }
    })

    test('Should handle infinite video loops correctly', () => {
      const videoId = 'infinite-video-test'
      const timeframes: TimeframeData[] = []
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: -1, // Infinite video loops
        loopMode: 'whole-video-plus-timeframes'
      }
      const queueLoopCount = 1 // Only 1 queue loop

      // Initialize video
      loopingManager.initializeVideo(videoId, timeframes, loopSettings, queueLoopCount)

      // Simulate video ending multiple times - should never complete due to infinite video loops
      for (let i = 0; i < 5; i++) {
        const result = loopingManager.handleVideoEnd(videoId, timeframes, loopSettings)
        const state = loopingManager.getState(videoId)!

        expect(result).toBe('continue')
        expect(state.videoLoopCount).toBe(i + 1)
        expect(mockOnVideoComplete).not.toHaveBeenCalled()
      }
    })
  })

  describe('Single Video With Timeframes', () => {
    test('Should handle timeframes progression correctly', () => {
      const videoId = 'single-video-timeframes-test'
      const timeframes: TimeframeData[] = [
        { id: 'tf1', startTime: 10, endTime: 20, loopCount: 2 },
        { id: 'tf2', startTime: 30, endTime: 40, loopCount: 1 }
      ]
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 2, // Video should loop 2 times
        loopMode: 'timeframes-only'
      }
      const queueLoopCount = -1

      // Initialize video
      loopingManager.initializeVideo(videoId, timeframes, loopSettings, queueLoopCount)
      let state = loopingManager.getState(videoId)!

      // Verify timeframes progression works
      // First timeframe end - should loop (1/2)
      loopingManager.handleTimeframeEnd(videoId, timeframes, loopSettings)
      state = loopingManager.getState(videoId)!
      expect(state.currentTimeframeIndex).toBe(0)
      expect(state.timeframeLoopCounts['tf1']).toBe(1)

      // Second timeframe end - should loop again (2/2)
      loopingManager.handleTimeframeEnd(videoId, timeframes, loopSettings)
      state = loopingManager.getState(videoId)!
      expect(state.currentTimeframeIndex).toBe(0)
      expect(state.timeframeLoopCounts['tf1']).toBe(2)

      // Third timeframe end - should move to next timeframe
      loopingManager.handleTimeframeEnd(videoId, timeframes, loopSettings)
      state = loopingManager.getState(videoId)!
      expect(state.currentTimeframeIndex).toBe(1)
      expect(state.timeframeLoopCounts['tf1']).toBe(2)

      // This proves that timeframes progression works correctly for single videos
      console.log('✅ Timeframes progression working correctly for single video')
    })
  })

  describe('Queue Progression Logic', () => {
    test('Should use queue looping only after video loops are exhausted', () => {
      // Test the rule engine logic directly
      const currentIndex = 0
      const totalVideos = 1 // Single video queue
      const queueLoopCount = 3

      // For single video queue at index 0, nextIndex would be 1
      // Since 1 >= 1 (totalVideos), it should check queue looping
      const action = ruleEngine.getQueueAction(currentIndex, totalVideos, queueLoopCount)
      expect(action).toBe('loop-queue') // Should loop back to start

      // After queue loop count is exhausted
      const actionAfterLoops = ruleEngine.getQueueAction(currentIndex, totalVideos, 0)
      expect(actionAfterLoops).toBe('queue-complete')
    })

    test('Should handle infinite queue loops correctly', () => {
      const action = ruleEngine.getQueueAction(0, 1, -1)
      expect(action).toBe('loop-queue')

      // Should always loop with infinite setting
      const actionStillInfinite = ruleEngine.getQueueAction(0, 1, -1)
      expect(actionStillInfinite).toBe('loop-queue')
    })
  })

  describe('Edge Cases', () => {
    test('Should handle video with videoLoopCount = 1 (no repeats)', () => {
      const videoId = 'no-repeat-test'
      const timeframes: TimeframeData[] = []
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 1, // Play once, no repeats
        loopMode: 'whole-video-plus-timeframes'
      }
      const queueLoopCount = 2

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, queueLoopCount)

      // Video should complete immediately after first play
      const result = loopingManager.handleVideoEnd(videoId, timeframes, loopSettings)
      expect(result).toBe('complete')
      expect(mockOnVideoComplete).toHaveBeenCalled()
    })

    test('Should handle empty queue gracefully', () => {
      // This tests the queue logic with 0 videos
      const action = ruleEngine.getQueueAction(0, 0, -1)
      expect(action).toBe('queue-complete') // No videos to loop
    })
  })
})

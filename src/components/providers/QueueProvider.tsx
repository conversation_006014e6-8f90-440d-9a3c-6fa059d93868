'use client'

import { ReactNode, useReducer, useMemo, useEffect, useRef } from 'react'
import { QueueContext } from '@/hooks/useQueue'
import { QueueState, QueueAction, QueueItem } from '@/lib/types/queue'
import { VideoMetadata } from '@/lib/types/video'
import { firebaseService } from '@/lib/services/firebase'
import { useAuth } from '@/hooks/useAuth'

interface QueueProviderProps {
  children: ReactNode
}

const initialState: QueueState = {
  items: [],
  currentIndex: 0,
  isPlaying: false,
  queueLoopCount: -1, // -1 for infinite
  shuffle: false,
  volume: 1,
  timestamp: Date.now(),
}

function queueReducer(state: QueueState, action: QueueAction): QueueState {
  switch (action.type) {
    case 'ADD_VIDEO':
      const currentItems = Array.isArray(state.items) ? state.items : []
      const now = Date.now()
      const newItem: QueueItem = {
        ...action.payload,
        addedAt: now + Math.random(), // Add small random component to ensure uniqueness
        queueIndex: currentItems.length,
        timeframes: [],
        loopSettings: {
          videoLoopCount: 1,
          loopMode: 'whole-video-plus-timeframes'
        }
      }
      return {
        ...state,
        items: [...currentItems, newItem],
        timestamp: now,
      }

    case 'REMOVE_VIDEO':
      const itemsToFilter = Array.isArray(state.items) ? state.items : []
      const filteredItems = itemsToFilter.filter((_, index) => index !== action.payload.index)
      return {
        ...state,
        items: filteredItems.map((item, index) => ({ ...item, queueIndex: index })),
        currentIndex: action.payload.index <= state.currentIndex ? Math.max(0, state.currentIndex - 1) : state.currentIndex,
        timestamp: Date.now(),
      }

    case 'MOVE_VIDEO':
      const { fromIndex, toIndex } = action.payload
      const itemsToMove = Array.isArray(state.items) ? [...state.items] : []
      if (fromIndex >= 0 && fromIndex < itemsToMove.length && toIndex >= 0 && toIndex < itemsToMove.length) {
        const [movedItem] = itemsToMove.splice(fromIndex, 1)
        itemsToMove.splice(toIndex, 0, movedItem)
      }
      return {
        ...state,
        items: itemsToMove.map((item, index) => ({ ...item, queueIndex: index })),
        timestamp: Date.now(),
      }

    case 'SET_CURRENT_INDEX':
      const itemsLength = Array.isArray(state.items) ? state.items.length : 0
      return {
        ...state,
        currentIndex: Math.max(0, Math.min(action.payload.index, itemsLength - 1)),
        timestamp: Date.now(),
      }

    case 'SET_PLAYING':
      return {
        ...state,
        isPlaying: action.payload.isPlaying,
        timestamp: Date.now(),
      }

    case 'SET_QUEUE_LOOP_COUNT':
      return {
        ...state,
        queueLoopCount: action.payload.queueLoopCount,
        timestamp: Date.now(),
      }

    case 'SET_SHUFFLE':
      return {
        ...state,
        shuffle: action.payload.shuffle,
        timestamp: Date.now(),
      }

    case 'SET_VOLUME':
      return {
        ...state,
        volume: Math.max(0, Math.min(1, action.payload.volume)),
        timestamp: Date.now(),
      }

    case 'CLEAR_QUEUE':
      return {
        ...state,
        items: [],
        currentIndex: 0,
        isPlaying: false,
        timestamp: Date.now(),
      }

    case 'LOAD_QUEUE':
      // Ensure the payload has the correct structure
      const loadedQueue = action.payload

      // Validate that the payload is a valid QueueState
      if (!loadedQueue || typeof loadedQueue !== 'object') {
        console.warn('Invalid queue data provided to LOAD_QUEUE')
        return state
      }

      // Ensure items array exists and is valid
      const items = Array.isArray(loadedQueue.items) ? loadedQueue.items : []

      return {
        ...initialState, // Start with a clean state
        ...loadedQueue,  // Apply the loaded data
        items,           // Ensure items is always an array
        currentIndex: Math.max(0, Math.min(loadedQueue.currentIndex || 0, items.length - 1)),
        timestamp: Date.now(),
      }

    case 'NEXT_VIDEO':
      const nextIndex = state.currentIndex + 1
      const itemsCount = Array.isArray(state.items) ? state.items.length : 0

      // If we're at the end of the queue
      if (nextIndex >= itemsCount) {
        // Check if we should loop (decrement first, then check)
        const newQueueLoopCount = state.queueLoopCount === -1 ? -1 : Math.max(0, state.queueLoopCount - 1)
        const shouldLoop = state.queueLoopCount === -1 || newQueueLoopCount > 0

        if (shouldLoop) {
          return {
            ...state,
            currentIndex: 0,
            queueLoopCount: newQueueLoopCount,
            timestamp: Date.now(),
          }
        } else {
          // Queue is complete - stay at current position
          return {
            ...state,
            timestamp: Date.now(),
          }
        }
      }

      return {
        ...state,
        currentIndex: nextIndex,
        timestamp: Date.now(),
      }

    case 'PREVIOUS_VIDEO':
      const prevIndex = state.currentIndex - 1
      const totalItems = Array.isArray(state.items) ? state.items.length : 0
      const shouldLoopPrev = state.queueLoopCount === -1 || state.queueLoopCount > 0

      // If we're at the beginning of the queue and need to loop
      if (prevIndex < 0 && shouldLoopPrev) {
        const newQueueLoopCount = state.queueLoopCount === -1 ? -1 : Math.max(0, state.queueLoopCount - 1)
        return {
          ...state,
          currentIndex: totalItems - 1,
          queueLoopCount: newQueueLoopCount,
          timestamp: Date.now(),
        }
      }

      return {
        ...state,
        currentIndex: prevIndex < 0 ? 0 : prevIndex,
        timestamp: Date.now(),
      }

    default:
      return state
  }
}

export function QueueProvider({ children }: QueueProviderProps) {
  const [state, dispatch] = useReducer(queueReducer, initialState)
  const { user } = useAuth()

  // Store the original queue loop count for repeat functionality
  const originalQueueLoopCountRef = useRef<number>(-1)

  // Initialize original loop count when the queue state changes
  useEffect(() => {
    if (originalQueueLoopCountRef.current === -1 && state.queueLoopCount !== -1) {
      originalQueueLoopCountRef.current = state.queueLoopCount
    }
  }, [state.queueLoopCount])

  // Auto-save queue to localStorage for persistence
  useEffect(() => {
    const saveToLocalStorage = () => {
      try {
        localStorage.setItem('youtube-looper-queue', JSON.stringify(state))
      } catch (error) {
        console.error('Failed to save queue to localStorage:', error)
      }
    }

    // Debounce saves
    const timeoutId = setTimeout(saveToLocalStorage, 1000)
    return () => clearTimeout(timeoutId)
  }, [state])

  // Load queue from localStorage on mount
  useEffect(() => {
    try {
      const savedQueue = localStorage.getItem('youtube-looper-queue')
      if (savedQueue) {
        const parsedQueue = JSON.parse(savedQueue)
        dispatch({ type: 'LOAD_QUEUE', payload: parsedQueue })
        console.log('✅ Queue loaded from localStorage')
      }
    } catch (error) {
      console.error('Failed to load queue from localStorage:', error)
    }
  }, [])

  // Queue management functions
  const addVideo = (video: VideoMetadata) => {
    dispatch({ type: 'ADD_VIDEO', payload: video })
  }

  const removeVideo = (index: number) => {
    dispatch({ type: 'REMOVE_VIDEO', payload: { index } })
  }

  const moveVideo = (fromIndex: number, toIndex: number) => {
    dispatch({ type: 'MOVE_VIDEO', payload: { fromIndex, toIndex } })
  }

  const clearQueue = () => {
    dispatch({ type: 'CLEAR_QUEUE' })
  }

  const loadQueue = (queue: QueueState) => {
    // Store the original loop count when loading a queue
    originalQueueLoopCountRef.current = queue.queueLoopCount
    dispatch({ type: 'LOAD_QUEUE', payload: queue })
  }

  // Playback control functions (placeholders)
  const playVideo = (index?: number) => {
    if (index !== undefined) {
      dispatch({ type: 'SET_CURRENT_INDEX', payload: { index } })
    }
    dispatch({ type: 'SET_PLAYING', payload: { isPlaying: true } })
  }

  const pauseVideo = () => {
    dispatch({ type: 'SET_PLAYING', payload: { isPlaying: false } })
  }

  const nextVideo = () => {
    dispatch({ type: 'NEXT_VIDEO' })
  }

  const previousVideo = () => {
    dispatch({ type: 'PREVIOUS_VIDEO' })
  }

  const seekTo = (seconds: number) => {
    console.log('Seek to:', seconds)
  }

  const setVolume = (volume: number) => {
    dispatch({ type: 'SET_VOLUME', payload: { volume } })
  }

  // State setters
  const setCurrentIndex = (index: number) => {
    dispatch({ type: 'SET_CURRENT_INDEX', payload: { index } })
  }

  const setPlaying = (isPlaying: boolean) => {
    dispatch({ type: 'SET_PLAYING', payload: { isPlaying } })
  }

  const setQueueLoopCount = (queueLoopCount: number) => {
    // Store the original loop count when it's manually set (not during automatic queue progression)
    // We detect queue progression by checking if the new value is exactly current - 1
    const isQueueProgression = queueLoopCount === state.queueLoopCount - 1 && state.queueLoopCount > 0

    if (!isQueueProgression) {
      originalQueueLoopCountRef.current = queueLoopCount
    }
    dispatch({ type: 'SET_QUEUE_LOOP_COUNT', payload: { queueLoopCount } })
  }

  const setShuffle = (shuffle: boolean) => {
    dispatch({ type: 'SET_SHUFFLE', payload: { shuffle } })
  }

  const restartQueue = (loopCount?: number) => {
    // Use the original loop count if no specific count is provided
    const targetLoopCount = loopCount !== undefined ? loopCount : originalQueueLoopCountRef.current

    dispatch({ type: 'SET_CURRENT_INDEX', payload: { index: 0 } })
    dispatch({ type: 'SET_QUEUE_LOOP_COUNT', payload: { queueLoopCount: targetLoopCount } })
  }

  // Queue operations with Firebase integration
  const saveQueue = async (title: string, isPublic = false): Promise<string | null> => {
    if (!user) {
      console.warn('User not authenticated, cannot save queue')
      return null
    }

    try {
      const queueId = await firebaseService.saveQueue(
        user.uid,
        state,
        {
          title,
          description: `Queue with ${Array.isArray(state.items) ? state.items.length : 0} videos`,
        },
        isPublic
      )

      if (queueId) {
        console.log(`✅ Queue saved ${isPublic ? 'publicly' : 'privately'}:`, queueId)
        return queueId
      }

      return null
    } catch (error) {
      console.error('❌ Failed to save queue:', error)
      return null
    }
  }



  const shareCurrentQueue = async (title: string): Promise<string | null> => {
    if (!user) {
      console.warn('User not authenticated, cannot share current queue')
      return null
    }

    if (state.items.length === 0) {
      console.warn('Cannot share empty queue')
      return null
    }

    try {
      const publicQueueId = await firebaseService.shareCurrentQueue(user.uid, state, title)
      if (publicQueueId) {
        console.log('✅ Current queue shared publicly:', publicQueueId)
        return publicQueueId
      }
      return null
    } catch (error) {
      console.error('❌ Failed to share current queue:', error)
      return null
    }
  }

  // Computed properties
  const currentVideo = useMemo(() => {
    // Safety check to ensure items array exists and is valid
    if (!Array.isArray(state.items) || state.items.length === 0) {
      return null
    }

    // Ensure currentIndex is within bounds
    const index = Math.max(0, Math.min(state.currentIndex, state.items.length - 1))
    return state.items[index] || null
  }, [state.items, state.currentIndex])

  const hasNext = useMemo(() => {
    if (!Array.isArray(state.items)) return false

    // If not at the end of queue, there's always a next video
    if (state.currentIndex < state.items.length - 1) return true

    // At the end of queue - check if we can loop
    const wouldLoopCount = state.queueLoopCount === -1 ? -1 : Math.max(0, state.queueLoopCount - 1)
    const shouldLoop = state.queueLoopCount === -1 || wouldLoopCount > 0
    return shouldLoop
  }, [state.currentIndex, state.items, state.queueLoopCount])

  const hasPrevious = useMemo(() => {
    const shouldLoop = state.queueLoopCount === -1 || state.queueLoopCount > 0
    return state.currentIndex > 0 || shouldLoop
  }, [state.currentIndex, state.queueLoopCount])

  const queueDuration = useMemo(() => {
    if (!Array.isArray(state.items)) return 0
    return state.items.reduce((total, item) => total + (item.duration || 0), 0)
  }, [state.items])

  const value = {
    ...state,
    addVideo,
    removeVideo,
    moveVideo,
    clearQueue,
    loadQueue,
    playVideo,
    pauseVideo,
    nextVideo,
    previousVideo,
    seekTo,
    setVolume,
    setCurrentIndex,
    setPlaying,
    setQueueLoopCount,
    setShuffle,
    restartQueue,
    currentVideo,
    hasNext,
    hasPrevious,
    queueDuration,
    saveQueue,
    shareCurrentQueue,
  }

  return (
    <QueueContext.Provider value={value}>
      {children}
    </QueueContext.Provider>
  )
}
